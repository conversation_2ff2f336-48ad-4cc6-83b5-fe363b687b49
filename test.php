<?php
require_once 'config.php';
require_once 'IELTSScorer.php';
require_once 'examples.php';

/**
 * Test script for IELTS Scorer
 */

echo "<h1>IELTS Scorer Test</h1>\n";

// Test with sample essay
$examples = IELTSExamples::getExamples();
$taskType = 'task2';
$example = $examples[$taskType];

echo "<h2>Testing with Task 2 Example</h2>\n";
echo "<h3>Prompt:</h3>\n";
echo "<p>" . htmlspecialchars($example['prompt']) . "</p>\n";

echo "<h3>Essay:</h3>\n";
echo "<div style='background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo nl2br(htmlspecialchars($example['sample_essay']));
echo "</div>\n";

echo "<h3>Expected Score: " . $example['expected_score'] . "</h3>\n";

// Initialize scorer
$scorer = new IELTSScorer();

echo "<h3>Scoring Result:</h3>\n";

try {
    $result = $scorer->scoreEssay($example['sample_essay'], $taskType, $example['prompt']);
    
    if (isset($result['error']) && $result['error']) {
        echo "<div style='color: red;'>Error: " . htmlspecialchars($result['message']) . "</div>\n";
        if (isset($result['raw_response'])) {
            echo "<h4>Raw Response:</h4>\n";
            echo "<pre>" . htmlspecialchars($result['raw_response']) . "</pre>\n";
        }
    } else {
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
        echo "<h4>Overall Band Score: " . ($result['overall_band_score'] ?? 'N/A') . "</h4>\n";
        
        if (isset($result['criteria_scores'])) {
            echo "<h4>Criteria Scores:</h4>\n";
            echo "<ul>\n";
            foreach ($result['criteria_scores'] as $criterion => $data) {
                echo "<li><strong>" . ucfirst(str_replace('_', ' ', $criterion)) . ":</strong> " . ($data['score'] ?? 'N/A') . "</li>\n";
            }
            echo "</ul>\n";
        }
        
        if (isset($result['metadata'])) {
            echo "<h4>Metadata:</h4>\n";
            echo "<ul>\n";
            echo "<li>Word count: " . $result['metadata']['word_count'] . "</li>\n";
            echo "<li>Task type: " . $result['metadata']['task_type'] . "</li>\n";
            echo "<li>Scored at: " . $result['metadata']['scored_at'] . "</li>\n";
            echo "</ul>\n";
        }
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Exception: " . htmlspecialchars($e->getMessage()) . "</div>\n";
}

// Test API connection
echo "<h2>API Connection Test</h2>\n";

try {
    $testPrompt = "Please respond with 'API connection successful' if you receive this message.";
    
    $data = [
        'model' => OPENAI_MODEL,
        'messages' => [
            [
                'role' => 'user',
                'content' => $testPrompt
            ]
        ],
        'max_tokens' => 50,
        'temperature' => 0.1
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, OPENAI_API_URL);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . OPENAI_API_KEY,
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        echo "<div style='color: red;'>cURL Error: " . curl_error($ch) . "</div>\n";
    } else {
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>\n";
        echo "<strong>HTTP Code:</strong> " . $httpCode . "<br>\n";
        
        if ($httpCode === 200) {
            $decodedResponse = json_decode($response, true);
            if ($decodedResponse && isset($decodedResponse['choices'][0]['message']['content'])) {
                echo "<strong>API Response:</strong> " . htmlspecialchars($decodedResponse['choices'][0]['message']['content']) . "<br>\n";
                echo "<span style='color: green;'>✓ API connection successful!</span>\n";
            } else {
                echo "<strong>Raw Response:</strong><br>\n";
                echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
            }
        } else {
            echo "<span style='color: red;'>✗ API connection failed</span><br>\n";
            echo "<strong>Response:</strong> " . htmlspecialchars($response) . "\n";
        }
        echo "</div>\n";
    }
    
    curl_close($ch);
    
} catch (Exception $e) {
    echo "<div style='color: red;'>API Test Exception: " . htmlspecialchars($e->getMessage()) . "</div>\n";
}

// Configuration check
echo "<h2>Configuration Check</h2>\n";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px;'>\n";
echo "<ul>\n";
echo "<li><strong>API URL:</strong> " . OPENAI_API_URL . "</li>\n";
echo "<li><strong>Model:</strong> " . OPENAI_MODEL . "</li>\n";
echo "<li><strong>Max Tokens:</strong> " . MAX_TOKENS . "</li>\n";
echo "<li><strong>Temperature:</strong> " . TEMPERATURE . "</li>\n";
echo "<li><strong>API Key:</strong> " . (OPENAI_API_KEY ? 'Set (***' . substr(OPENAI_API_KEY, -4) . ')' : 'Not set') . "</li>\n";
echo "<li><strong>Debug Mode:</strong> " . (DEBUG_MODE ? 'Enabled' : 'Disabled') . "</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<h2>System Requirements Check</h2>\n";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px;'>\n";
echo "<ul>\n";
echo "<li><strong>PHP Version:</strong> " . phpversion() . " " . (version_compare(phpversion(), '7.4.0', '>=') ? '✓' : '✗ (Requires 7.4+)') . "</li>\n";
echo "<li><strong>cURL Extension:</strong> " . (extension_loaded('curl') ? '✓ Available' : '✗ Not available') . "</li>\n";
echo "<li><strong>JSON Extension:</strong> " . (extension_loaded('json') ? '✓ Available' : '✗ Not available') . "</li>\n";
echo "<li><strong>OpenSSL Extension:</strong> " . (extension_loaded('openssl') ? '✓ Available' : '✗ Not available') . "</li>\n";
echo "</ul>\n";
echo "</div>\n";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}

ul {
    margin: 10px 0;
}

li {
    margin: 5px 0;
}
</style>
