<!DOCTYPE html>
<html>
<head>
    <title>API Cleaning Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .correction { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body><h1>API Data Cleaning Test</h1><div class='success'>Testing with essay: Many people believes that technology have changed our lifes dramatically. In my opinion, I think that this changes has both positive and negative effects on society.</div><div class='success'>Scoring successful! Overall score: 5</div><h3>Highlighted Corrections (5 found):</h3><div class='correction'><h4>Correction 1</h4><strong>Original Text:</strong> "Many people believes"<br><strong>Suggested:</strong> "Many people believe"<br><strong>Type:</strong> grammar<br><strong>Severity:</strong> high<br><strong>Explanation:</strong> Subject-verb agreement error.<br><div style='color: green; font-weight: bold;'>Γ£à Clean text</div></div><div class='correction'><h4>Correction 2</h4><strong>Original Text:</strong> "have changed our lifes"<br><strong>Suggested:</strong> "have changed our lives"<br><strong>Type:</strong> grammar<br><strong>Severity:</strong> high<br><strong>Explanation:</strong> Spelling error (&#039;lifes&#039; should be &#039;lives&#039;).<br><div style='color: green; font-weight: bold;'>Γ£à Clean text</div></div><div class='correction'><h4>Correction 3</h4><strong>Original Text:</strong> "this changes has both positive and negative effects"<br><strong>Suggested:</strong> "these changes have both positive and negative effects"<br><strong>Type:</strong> grammar<br><strong>Severity:</strong> high<br><strong>Explanation:</strong> Subject-verb agreement and pluralization error.<br><div style='color: green; font-weight: bold;'>Γ£à Clean text</div></div><div class='correction'><h4>Correction 4</h4><strong>Original Text:</strong> "In my opinion, I think that this changes has both positive and negative effects on society."<br><strong>Suggested:</strong> "In my opinion, technology has both positive and negative effects on society."<br><strong>Type:</strong> coherence<br><strong>Severity:</strong> medium<br><strong>Explanation:</strong> Redundant phrasing and lack of clarity.<br><div style='color: green; font-weight: bold;'>Γ£à Clean text</div></div><div class='correction'><h4>Correction 5</h4><strong>Original Text:</strong> "I think that this changes has both positive and negative effects"<br><strong>Suggested:</strong> "I believe that these changes have both positive and negative impacts"<br><strong>Type:</strong> vocabulary<br><strong>Severity:</strong> medium<br><strong>Explanation:</strong> Improving word choice for clarity and precision.<br><div style='color: green; font-weight: bold;'>Γ£à Clean text</div></div><h3>Test Highlighting:</h3><div id='testHighlighting'></div><button onclick='testHighlighting()' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>Test Highlighting</button><details style='margin-top: 20px;'><summary>Full API Response</summary><pre>{
    &quot;overall_band_score&quot;: 5,
    &quot;criteria_scores&quot;: {
        &quot;task_achievement&quot;: {
            &quot;score&quot;: 5,
            &quot;analysis&quot;: &quot;The response addresses the task but lacks depth and specific examples to support the arguments. The position is stated but not effectively developed.&quot;,
            &quot;strengths&quot;: [
                &quot;Identifies both positive and negative effects of technology.&quot;
            ],
            &quot;weaknesses&quot;: [
                &quot;Lacks specific examples to illustrate points.&quot;,
                &quot;The argument is not fully developed.&quot;
            ],
            &quot;suggestions&quot;: [
                &quot;Provide specific examples for both positive and negative effects.&quot;,
                &quot;Develop arguments more thoroughly.&quot;
            ]
        },
        &quot;coherence_cohesion&quot;: {
            &quot;score&quot;: 5,
            &quot;analysis&quot;: &quot;The essay has a basic structure but lacks clear linking between ideas. The flow of information is somewhat disjointed.&quot;,
            &quot;strengths&quot;: [
                &quot;Basic structure is present.&quot;
            ],
            &quot;weaknesses&quot;: [
                &quot;Transitions between ideas are weak.&quot;,
                &quot;Paragraphs do not clearly delineate different points.&quot;
            ],
            &quot;suggestions&quot;: [
                &quot;Use linking words to connect ideas more clearly.&quot;,
                &quot;Ensure each paragraph focuses on a single idea.&quot;
            ]
        },
        &quot;lexical_resource&quot;: {
            &quot;score&quot;: 5,
            &quot;analysis&quot;: &quot;The vocabulary used is basic and contains several errors. There is a lack of variety in word choice.&quot;,
            &quot;strengths&quot;: [
                &quot;Attempts to use some relevant vocabulary.&quot;
            ],
            &quot;weaknesses&quot;: [
                &quot;Frequent spelling errors (e.g., &#039;lifes&#039;).&quot;,
                &quot;Limited range of vocabulary.&quot;
            ],
            &quot;suggestions&quot;: [
                &quot;Use a wider range of vocabulary.&quot;,
                &quot;Check spelling and word forms.&quot;
            ]
        },
        &quot;grammatical_range&quot;: {
            &quot;score&quot;: 5,
            &quot;analysis&quot;: &quot;There are several grammatical errors that affect clarity. The range of grammatical structures is limited.&quot;,
            &quot;strengths&quot;: [
                &quot;Some correct sentence structures.&quot;
            ],
            &quot;weaknesses&quot;: [
                &quot;Errors in subject-verb agreement.&quot;,
                &quot;Incorrect use of tenses.&quot;
            ],
            &quot;suggestions&quot;: [
                &quot;Review basic grammar rules, especially subject-verb agreement.&quot;,
                &quot;Practice using more complex sentence structures.&quot;
            ]
        }
    },
    &quot;detailed_feedback&quot;: {
        &quot;introduction&quot;: &quot;The introduction states the topic but lacks a clear thesis statement outlining the main points to be discussed.&quot;,
        &quot;body_paragraphs&quot;: [
            &quot;Body paragraphs are not clearly defined and lack specific examples.&quot;,
            &quot;Each paragraph should focus on either positive or negative effects.&quot;
        ],
        &quot;conclusion&quot;: &quot;There is no conclusion present, which is essential to summarize the main points discussed.&quot;,
        &quot;overall_structure&quot;: &quot;The essay needs a clearer structure with distinct introduction, body paragraphs, and conclusion.&quot;
    },
    &quot;improvement_priorities&quot;: [
        &quot;Developing arguments with specific examples.&quot;,
        &quot;Improving coherence and cohesion.&quot;,
        &quot;Enhancing vocabulary and grammar.&quot;
    ],
    &quot;estimated_study_time&quot;: &quot;Approximately 20 hours of focused study on writing techniques and grammar.&quot;,
    &quot;sample_improvements&quot;: {
        &quot;vocabulary&quot;: [
            &quot;Instead of &#039;changed our lifes&#039;, use &#039;transformed our lives&#039;.&quot;,
            &quot;Use &#039;benefits&#039; and &#039;drawbacks&#039; instead of &#039;positive and negative effects&#039;.&quot;
        ],
        &quot;grammar&quot;: [
            &quot;Change &#039;believes&#039; to &#039;believe&#039; for subject-verb agreement.&quot;,
            &quot;Use &#039;these changes&#039; instead of &#039;this changes&#039;.&quot;
        ],
        &quot;structure&quot;: [
            &quot;Create distinct paragraphs for positive and negative effects.&quot;,
            &quot;Add a conclusion summarizing the main points.&quot;
        ]
    },
    &quot;highlighted_corrections&quot;: [
        {
            &quot;original_text&quot;: &quot;Many people believes&quot;,
            &quot;suggested_correction&quot;: &quot;Many people believe&quot;,
            &quot;error_type&quot;: &quot;grammar&quot;,
            &quot;explanation&quot;: &quot;Subject-verb agreement error.&quot;,
            &quot;severity&quot;: &quot;high&quot;
        },
        {
            &quot;original_text&quot;: &quot;have changed our lifes&quot;,
            &quot;suggested_correction&quot;: &quot;have changed our lives&quot;,
            &quot;error_type&quot;: &quot;grammar&quot;,
            &quot;explanation&quot;: &quot;Spelling error (&#039;lifes&#039; should be &#039;lives&#039;).&quot;,
            &quot;severity&quot;: &quot;high&quot;
        },
        {
            &quot;original_text&quot;: &quot;this changes has both positive and negative effects&quot;,
            &quot;suggested_correction&quot;: &quot;these changes have both positive and negative effects&quot;,
            &quot;error_type&quot;: &quot;grammar&quot;,
            &quot;explanation&quot;: &quot;Subject-verb agreement and pluralization error.&quot;,
            &quot;severity&quot;: &quot;high&quot;
        },
        {
            &quot;original_text&quot;: &quot;In my opinion, I think that this changes has both positive and negative effects on society.&quot;,
            &quot;suggested_correction&quot;: &quot;In my opinion, technology has both positive and negative effects on society.&quot;,
            &quot;error_type&quot;: &quot;coherence&quot;,
            &quot;explanation&quot;: &quot;Redundant phrasing and lack of clarity.&quot;,
            &quot;severity&quot;: &quot;medium&quot;
        },
        {
            &quot;original_text&quot;: &quot;I think that this changes has both positive and negative effects&quot;,
            &quot;suggested_correction&quot;: &quot;I believe that these changes have both positive and negative impacts&quot;,
            &quot;error_type&quot;: &quot;vocabulary&quot;,
            &quot;explanation&quot;: &quot;Improving word choice for clarity and precision.&quot;,
            &quot;severity&quot;: &quot;medium&quot;
        }
    ],
    &quot;annotated_essay&quot;: &quot;Many people [CORRECTION: believes -&gt; believe] that technology [CORRECTION: have changed -&gt; has changed] our [CORRECTION: lifes -&gt; lives] dramatically. In my opinion, [CORRECTION: I think that this changes has -&gt; I believe that these changes have] both positive and negative effects on society.&quot;,
    &quot;metadata&quot;: {
        &quot;task_type&quot;: &quot;task2&quot;,
        &quot;word_count&quot;: 26,
        &quot;scored_at&quot;: &quot;2025-07-17 23:24:16&quot;,
        &quot;essay_length&quot;: 165
    }
}</pre></details>
<script src='assets/essay-highlighter.js'></script>
<script>
function testHighlighting() {
    const result = {"overall_band_score":5,"criteria_scores":{"task_achievement":{"score":5,"analysis":"The response addresses the task but lacks depth and specific examples to support the arguments. The position is stated but not effectively developed.","strengths":["Identifies both positive and negative effects of technology."],"weaknesses":["Lacks specific examples to illustrate points.","The argument is not fully developed."],"suggestions":["Provide specific examples for both positive and negative effects.","Develop arguments more thoroughly."]},"coherence_cohesion":{"score":5,"analysis":"The essay has a basic structure but lacks clear linking between ideas. The flow of information is somewhat disjointed.","strengths":["Basic structure is present."],"weaknesses":["Transitions between ideas are weak.","Paragraphs do not clearly delineate different points."],"suggestions":["Use linking words to connect ideas more clearly.","Ensure each paragraph focuses on a single idea."]},"lexical_resource":{"score":5,"analysis":"The vocabulary used is basic and contains several errors. There is a lack of variety in word choice.","strengths":["Attempts to use some relevant vocabulary."],"weaknesses":["Frequent spelling errors (e.g., 'lifes').","Limited range of vocabulary."],"suggestions":["Use a wider range of vocabulary.","Check spelling and word forms."]},"grammatical_range":{"score":5,"analysis":"There are several grammatical errors that affect clarity. The range of grammatical structures is limited.","strengths":["Some correct sentence structures."],"weaknesses":["Errors in subject-verb agreement.","Incorrect use of tenses."],"suggestions":["Review basic grammar rules, especially subject-verb agreement.","Practice using more complex sentence structures."]}},"detailed_feedback":{"introduction":"The introduction states the topic but lacks a clear thesis statement outlining the main points to be discussed.","body_paragraphs":["Body paragraphs are not clearly defined and lack specific examples.","Each paragraph should focus on either positive or negative effects."],"conclusion":"There is no conclusion present, which is essential to summarize the main points discussed.","overall_structure":"The essay needs a clearer structure with distinct introduction, body paragraphs, and conclusion."},"improvement_priorities":["Developing arguments with specific examples.","Improving coherence and cohesion.","Enhancing vocabulary and grammar."],"estimated_study_time":"Approximately 20 hours of focused study on writing techniques and grammar.","sample_improvements":{"vocabulary":["Instead of 'changed our lifes', use 'transformed our lives'.","Use 'benefits' and 'drawbacks' instead of 'positive and negative effects'."],"grammar":["Change 'believes' to 'believe' for subject-verb agreement.","Use 'these changes' instead of 'this changes'."],"structure":["Create distinct paragraphs for positive and negative effects.","Add a conclusion summarizing the main points."]},"highlighted_corrections":[{"original_text":"Many people believes","suggested_correction":"Many people believe","error_type":"grammar","explanation":"Subject-verb agreement error.","severity":"high"},{"original_text":"have changed our lifes","suggested_correction":"have changed our lives","error_type":"grammar","explanation":"Spelling error ('lifes' should be 'lives').","severity":"high"},{"original_text":"this changes has both positive and negative effects","suggested_correction":"these changes have both positive and negative effects","error_type":"grammar","explanation":"Subject-verb agreement and pluralization error.","severity":"high"},{"original_text":"In my opinion, I think that this changes has both positive and negative effects on society.","suggested_correction":"In my opinion, technology has both positive and negative effects on society.","error_type":"coherence","explanation":"Redundant phrasing and lack of clarity.","severity":"medium"},{"original_text":"I think that this changes has both positive and negative effects","suggested_correction":"I believe that these changes have both positive and negative impacts","error_type":"vocabulary","explanation":"Improving word choice for clarity and precision.","severity":"medium"}],"annotated_essay":"Many people [CORRECTION: believes -> believe] that technology [CORRECTION: have changed -> has changed] our [CORRECTION: lifes -> lives] dramatically. In my opinion, [CORRECTION: I think that this changes has -> I believe that these changes have] both positive and negative effects on society.","metadata":{"task_type":"task2","word_count":26,"scored_at":"2025-07-17 23:24:16","essay_length":165}};
    const essay = "Many people believes that technology have changed our lifes dramatically. In my opinion, I think that this changes has both positive and negative effects on society.";
    
    console.log('Testing highlighting with:', result);
    
    if (result && result.highlighted_corrections && essay) {
        const highlighter = new EssayHighlighter('testHighlighting');
        highlighter.init(result, essay);
    } else {
        document.getElementById('testHighlighting').innerHTML = '<div class="error">No valid data for highlighting test</div>';
    }
}
</script>
</body>
</html>
