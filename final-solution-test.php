<?php
require_once 'config.php';
require_once 'IELTSScorer.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Final Solution Test</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <link href='assets/essay-highlighter.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; min-height: 100vh; }
        .container { max-width: 1200px; background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .hero { background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; }
        .success-card { background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
        .fail-card { background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; }
        .correction-clean { background: #d4edda; border-left: 5px solid #28a745; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .correction-dirty { background: #f8d7da; border-left: 5px solid #dc3545; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .highlight-test { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px dashed #007bff; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<div class='hero'>";
echo "<h1><i class='fas fa-rocket'></i> FINAL SOLUTION TEST</h1>";
echo "<p class='lead'>Ultimate test of the complete highlighting system with nuclear cleaning + new prompt</p>";
echo "</div>";

$testEssay = "Many people believes that education is important than ever. Students should only learn knowledge from books to do well in exam, and they don't need practical skill.";
$taskType = 'task2';
$prompt = 'Education is important. Discuss the role of books versus practical skills.';

try {
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-edit'></i> Test Essay:</h5>";
    echo "<p><em>" . htmlspecialchars($testEssay) . "</em></p>";
    echo "</div>";
    
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-cogs'></i> <strong>Deploying final solution:</strong> New prompt + Nuclear cleaning + Enhanced validation";
    echo "</div>";
    
    $scorer = new IELTSScorer();
    $result = $scorer->scoreEssay($testEssay, $taskType, $prompt);
    
    if (isset($result['error']) && $result['error']) {
        echo "<div class='fail-card'>";
        echo "<h4><i class='fas fa-times'></i> System Error</h4>";
        echo "<p>Error: " . htmlspecialchars($result['message']) . "</p>";
        echo "</div>";
    } else {
        echo "<div class='success-card'>";
        echo "<h4><i class='fas fa-check'></i> API Response Received</h4>";
        echo "<p>Overall Band Score: <strong>" . ($result['overall_band_score'] ?? 'N/A') . "</strong></p>";
        echo "</div>";
        
        if (isset($result['highlighted_corrections']) && is_array($result['highlighted_corrections'])) {
            $totalCorrections = count($result['highlighted_corrections']);
            $perfectCorrections = 0;
            $problematicCorrections = 0;
            
            echo "<h3><i class='fas fa-microscope'></i> Correction Quality Analysis</h3>";
            
            foreach ($result['highlighted_corrections'] as $i => $correction) {
                $originalText = $correction['original_text'] ?? '';
                $suggestedText = $correction['suggested_correction'] ?? '';
                $explanation = $correction['explanation'] ?? '';
                
                // Ultra strict validation
                $isPerfect = (
                    !empty($originalText) && 
                    !empty($suggestedText) &&
                    strlen($originalText) > 2 &&
                    strpos($originalText, 'data-') === false &&
                    strpos($originalText, '<') === false &&
                    strpos($originalText, '>') === false &&
                    strpos($originalText, '=') === false &&
                    strpos($originalText, 'correction') === false &&
                    strpos($suggestedText, 'data-') === false &&
                    strpos($suggestedText, '<') === false &&
                    strpos($explanation, 'data-') === false &&
                    strpos($explanation, '<') === false
                );
                
                if ($isPerfect) {
                    $perfectCorrections++;
                    echo "<div class='correction-clean'>";
                    echo "<h5><i class='fas fa-gem text-success'></i> Perfect Correction " . ($i + 1) . "</h5>";
                    echo "<div class='row'>";
                    echo "<div class='col-md-6'>";
                    echo "<strong>Original:</strong> \"<span class='text-primary'>" . htmlspecialchars($originalText) . "</span>\"<br>";
                    echo "<strong>Suggested:</strong> \"<span class='text-success'>" . htmlspecialchars($suggestedText) . "</span>\"<br>";
                    echo "</div>";
                    echo "<div class='col-md-6'>";
                    echo "<strong>Type:</strong> " . htmlspecialchars($correction['error_type'] ?? 'N/A') . "<br>";
                    echo "<strong>Severity:</strong> " . htmlspecialchars($correction['severity'] ?? 'N/A') . "<br>";
                    echo "</div>";
                    echo "</div>";
                    echo "<strong>Explanation:</strong> " . htmlspecialchars($explanation) . "<br>";
                    echo "<div class='mt-2'><span class='badge bg-success'><i class='fas fa-check'></i> READY FOR HIGHLIGHTING</span></div>";
                    echo "</div>";
                } else {
                    $problematicCorrections++;
                    echo "<div class='correction-dirty'>";
                    echo "<h5><i class='fas fa-exclamation-triangle text-danger'></i> Problematic Correction " . ($i + 1) . "</h5>";
                    echo "<strong>Original:</strong> \"" . htmlspecialchars($originalText) . "\"<br>";
                    echo "<strong>Issues:</strong> ";
                    if (strpos($originalText, 'data-') !== false) echo "<span class='badge bg-danger'>data-attributes</span> ";
                    if (strpos($originalText, '<') !== false) echo "<span class='badge bg-danger'>HTML-tags</span> ";
                    if (strpos($originalText, '=') !== false) echo "<span class='badge bg-danger'>equals-signs</span> ";
                    if (empty($originalText)) echo "<span class='badge bg-danger'>empty-text</span> ";
                    echo "</div>";
                }
            }
            
            // Final verdict
            echo "<div class='row mt-4'>";
            echo "<div class='col-md-6'>";
            if ($perfectCorrections === $totalCorrections && $perfectCorrections > 0) {
                echo "<div class='success-card text-center'>";
                echo "<h3><i class='fas fa-trophy'></i> COMPLETE SUCCESS!</h3>";
                echo "<h4>🎉 ALL {$totalCorrections} CORRECTIONS ARE PERFECT! 🎉</h4>";
                echo "<p>Nuclear cleaning + New prompt = 100% success rate</p>";
                echo "<p><strong>Highlighting system is now fully operational!</strong></p>";
                echo "</div>";
                
                // Test highlighting
                echo "</div>";
                echo "<div class='col-md-6'>";
                echo "<div class='highlight-test'>";
                echo "<h5><i class='fas fa-highlighter'></i> Live Highlighting Test</h5>";
                echo "<div id='highlightingDemo'></div>";
                echo "<button class='btn btn-primary btn-lg mt-3' onclick='testHighlighting()'>";
                echo "<i class='fas fa-play'></i> Test Highlighting Now";
                echo "</button>";
                echo "</div>";
                
            } else if ($perfectCorrections > 0) {
                echo "<div class='alert alert-warning text-center'>";
                echo "<h4><i class='fas fa-exclamation-triangle'></i> Partial Success</h4>";
                echo "<p><strong>{$perfectCorrections}</strong> perfect corrections out of {$totalCorrections}</p>";
                echo "<p>{$problematicCorrections} corrections still have issues</p>";
                echo "</div>";
            } else {
                echo "<div class='fail-card text-center'>";
                echo "<h3><i class='fas fa-skull-crossbones'></i> SYSTEM FAILURE</h3>";
                echo "<p>No perfect corrections found. AI is still returning HTML.</p>";
                echo "<p>Further intervention required.</p>";
                echo "</div>";
            }
            echo "</div>";
            
        } else {
            echo "<div class='fail-card'>";
            echo "<h4><i class='fas fa-times'></i> No Corrections Found</h4>";
            echo "<p>The API response did not contain highlighted_corrections array.</p>";
            echo "</div>";
        }
    }
    
    echo "<details class='mt-4'>";
    echo "<summary class='btn btn-outline-secondary'><i class='fas fa-code'></i> View Raw Response</summary>";
    echo "<pre class='bg-light p-3 rounded mt-3'>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>";
    echo "</details>";
    
} catch (Exception $e) {
    echo "<div class='fail-card'>";
    echo "<h4><i class='fas fa-bomb'></i> System Exception</h4>";
    echo "<p>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

echo "
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
<script src='assets/essay-highlighter.js'></script>
<script>
function testHighlighting() {
    const result = " . json_encode($result ?? []) . ";
    const essay = " . json_encode($testEssay) . ";
    
    console.log('Testing highlighting with final solution data:', result);
    
    if (result && result.highlighted_corrections && essay) {
        try {
            const highlighter = new EssayHighlighter('highlightingDemo');
            highlighter.init(result, essay);
            
            // Show success message
            setTimeout(() => {
                const successDiv = document.createElement('div');
                successDiv.className = 'alert alert-success mt-3';
                successDiv.innerHTML = '<i class=\"fas fa-check-circle\"></i> <strong>SUCCESS!</strong> Highlighting system is fully operational!';
                document.getElementById('highlightingDemo').appendChild(successDiv);
            }, 1000);
            
        } catch (error) {
            console.error('Highlighting failed:', error);
            document.getElementById('highlightingDemo').innerHTML = 
                '<div class=\"alert alert-danger\"><i class=\"fas fa-times\"></i> Highlighting failed: ' + error.message + '</div>';
        }
    } else {
        document.getElementById('highlightingDemo').innerHTML = 
            '<div class=\"alert alert-warning\"><i class=\"fas fa-exclamation-triangle\"></i> No valid data for highlighting test</div>';
    }
}
</script>
</body>
</html>";
?>
