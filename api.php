<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'IELTSScorer.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $essay = $input['essay'] ?? '';
    $taskType = $input['task_type'] ?? '';
    $prompt = $input['prompt'] ?? '';
    
    if (empty($essay)) {
        throw new Exception('Essay text is required');
    }
    
    if (empty($taskType)) {
        throw new Exception('Task type is required');
    }
    
    // Create scorer and process
    $scorer = new IELTSScorer();
    $result = $scorer->scoreEssay($essay, $taskType, $prompt);
    
    // Add request metadata
    $result['request_metadata'] = [
        'timestamp' => date('Y-m-d H:i:s'),
        'essay_length' => strlen($essay),
        'word_count' => str_word_count($essay),
        'task_type' => $taskType,
        'processing_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
    ];
    
    // Return JSON response
    echo json_encode($result, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => $e->getMessage(),
        'debug_info' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ], JSON_PRETTY_PRINT);
}
?>
