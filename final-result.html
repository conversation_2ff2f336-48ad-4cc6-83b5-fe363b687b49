<!DOCTYPE html>
<html>
<head>
    <title>Final API Test - Complete Solution</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <link href='assets/essay-highlighter.css' rel='stylesheet'>
    <style>
        body { background: #f8f9fa; padding: 20px; }
        .container { max-width: 1200px; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .correction { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #dee2e6; border-radius: 10px; }
    </style>
</head>
<body><div class='container'><h1><i class='fas fa-rocket'></i> Final API Test - Complete Solution</h1><p class='lead'>Testing the complete highlighting system with ultra-aggressive cleaning</p><div class='test-section'><h3><i class='fas fa-edit'></i> Test Essay</h3><div class='alert alert-info'>Many people believes that education is important than ever. Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing.</div></div><div class='test-section'><h3><i class='fas fa-cog'></i> API Processing</h3><p>Calling OpenAI API with ultra-aggressive cleaning...</p><div class='success'><i class='fas fa-check'></i> API call successful! Overall score: 5.5</div><h4>Corrections Analysis (5 found):</h4><div class='correction'><h5><i class='fas fa-check-circle text-success'></i> Correction 1 - CLEAN</h5><strong>Original:</strong> "Many people believes that education is important than ever."<br><strong>Suggested:</strong> "Many people believe that education is more important than ever."<br><strong>Type:</strong> grammar<br><strong>Severity:</strong> high<br><strong>Explanation:</strong> Subject-verb agreement error; &#039;people&#039; is plural, so &#039;believes&#039; should be &#039;believe&#039;. Also, &#039;important than ever&#039; should be &#039;more important than ever&#039; for correct comparative structure.<br><div class='text-success'><i class='fas fa-thumbs-up'></i> Ready for highlighting</div></div><div class='correction'><h5><i class='fas fa-check-circle text-success'></i> Correction 2 - CLEAN</h5><strong>Original:</strong> "Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing."<br><strong>Suggested:</strong> "Students should only learn knowledge from books to do well in exams and not waste time on skills like cooking or dressing."<br><strong>Type:</strong> grammar<br><strong>Severity:</strong> high<br><strong>Explanation:</strong> Missing plural form for &#039;exam&#039; and &#039;skill&#039;; &#039;skills&#039; should be plural to match &#039;cooking or dressing&#039;.<br><div class='text-success'><i class='fas fa-thumbs-up'></i> Ready for highlighting</div></div><div class='correction'><h5><i class='fas fa-check-circle text-success'></i> Correction 3 - CLEAN</h5><strong>Original:</strong> "and not waste time on skill like cooking or dressing."<br><strong>Suggested:</strong> "and not waste time on skills such as cooking or dressing."<br><strong>Type:</strong> vocabulary<br><strong>Severity:</strong> medium<br><strong>Explanation:</strong> &#039;such as&#039; is more appropriate than &#039;like&#039; in formal writing.<br><div class='text-success'><i class='fas fa-thumbs-up'></i> Ready for highlighting</div></div><div class='correction'><h5><i class='fas fa-check-circle text-success'></i> Correction 4 - CLEAN</h5><strong>Original:</strong> "Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing."<br><strong>Suggested:</strong> "Students should focus on learning knowledge from books to excel in exams rather than wasting time on practical skills such as cooking or dressing."<br><strong>Type:</strong> coherence<br><strong>Severity:</strong> medium<br><strong>Explanation:</strong> The sentence structure is awkward and could be clearer. A more cohesive structure improves clarity.<br><div class='text-success'><i class='fas fa-thumbs-up'></i> Ready for highlighting</div></div><div class='correction'><h5><i class='fas fa-check-circle text-success'></i> Correction 5 - CLEAN</h5><strong>Original:</strong> "Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing."<br><strong>Suggested:</strong> "Students should prioritize learning theoretical knowledge from books to perform well in exams instead of spending time on practical skills like cooking or dressing."<br><strong>Type:</strong> task_response<br><strong>Severity:</strong> high<br><strong>Explanation:</strong> The response lacks a balanced discussion of both books and practical skills; it should present a more nuanced view.<br><div class='text-success'><i class='fas fa-thumbs-up'></i> Ready for highlighting</div></div><div class='test-section'><h3><i class='fas fa-chart-bar'></i> Cleaning Results Summary</h3><div class='success'><h4><i class='fas fa-trophy'></i> SUCCESS!</h4><p><strong>5</strong> out of 5 corrections are clean and ready for highlighting.</p></div></div><div class='test-section'><h3><i class='fas fa-highlighter'></i> Highlighting Test</h3><p>Testing the highlighting system with clean data...</p><div id='highlightingTest'></div><button class='btn btn-primary' onclick='testHighlighting()'><i class='fas fa-play'></i> Test Highlighting</button></div><details class='mt-4'><summary><i class='fas fa-code'></i> Raw API Response (for debugging)</summary><pre>{
    &quot;overall_band_score&quot;: 5.5,
    &quot;highlighted_corrections&quot;: [
        {
            &quot;original_text&quot;: &quot;Many people believes that education is important than ever.&quot;,
            &quot;suggested_correction&quot;: &quot;Many people believe that education is more important than ever.&quot;,
            &quot;error_type&quot;: &quot;grammar&quot;,
            &quot;explanation&quot;: &quot;Subject-verb agreement error; &#039;people&#039; is plural, so &#039;believes&#039; should be &#039;believe&#039;. Also, &#039;important than ever&#039; should be &#039;more important than ever&#039; for correct comparative structure.&quot;,
            &quot;severity&quot;: &quot;high&quot;
        },
        {
            &quot;original_text&quot;: &quot;Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing.&quot;,
            &quot;suggested_correction&quot;: &quot;Students should only learn knowledge from books to do well in exams and not waste time on skills like cooking or dressing.&quot;,
            &quot;error_type&quot;: &quot;grammar&quot;,
            &quot;explanation&quot;: &quot;Missing plural form for &#039;exam&#039; and &#039;skill&#039;; &#039;skills&#039; should be plural to match &#039;cooking or dressing&#039;.&quot;,
            &quot;severity&quot;: &quot;high&quot;
        },
        {
            &quot;original_text&quot;: &quot;and not waste time on skill like cooking or dressing.&quot;,
            &quot;suggested_correction&quot;: &quot;and not waste time on skills such as cooking or dressing.&quot;,
            &quot;error_type&quot;: &quot;vocabulary&quot;,
            &quot;explanation&quot;: &quot;&#039;such as&#039; is more appropriate than &#039;like&#039; in formal writing.&quot;,
            &quot;severity&quot;: &quot;medium&quot;
        },
        {
            &quot;original_text&quot;: &quot;Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing.&quot;,
            &quot;suggested_correction&quot;: &quot;Students should focus on learning knowledge from books to excel in exams rather than wasting time on practical skills such as cooking or dressing.&quot;,
            &quot;error_type&quot;: &quot;coherence&quot;,
            &quot;explanation&quot;: &quot;The sentence structure is awkward and could be clearer. A more cohesive structure improves clarity.&quot;,
            &quot;severity&quot;: &quot;medium&quot;
        },
        {
            &quot;original_text&quot;: &quot;Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing.&quot;,
            &quot;suggested_correction&quot;: &quot;Students should prioritize learning theoretical knowledge from books to perform well in exams instead of spending time on practical skills like cooking or dressing.&quot;,
            &quot;error_type&quot;: &quot;task_response&quot;,
            &quot;explanation&quot;: &quot;The response lacks a balanced discussion of both books and practical skills; it should present a more nuanced view.&quot;,
            &quot;severity&quot;: &quot;high&quot;
        }
    ],
    &quot;annotated_essay&quot;: &quot;Many people believes [CORRECTION: believes - believe] that education is important [CORRECTION: important - more important] than ever. Students should only learn knowledge from books to do well in exam [CORRECTION: exam - exams], and not waste time on skill [CORRECTION: skill - skills] like cooking or dressing. [COMMENT: The essay lacks depth and fails to address both sides of the argument adequately. A more balanced approach is needed.]&quot;,
    &quot;metadata&quot;: {
        &quot;task_type&quot;: &quot;task2&quot;,
        &quot;word_count&quot;: 31,
        &quot;scored_at&quot;: &quot;2025-07-17 23:34:48&quot;,
        &quot;essay_length&quot;: 181
    }
}</pre></details></div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
<script src='assets/essay-highlighter.js'></script>
<script>
function testHighlighting() {
    const result = {"overall_band_score":5.5,"highlighted_corrections":[{"original_text":"Many people believes that education is important than ever.","suggested_correction":"Many people believe that education is more important than ever.","error_type":"grammar","explanation":"Subject-verb agreement error; 'people' is plural, so 'believes' should be 'believe'. Also, 'important than ever' should be 'more important than ever' for correct comparative structure.","severity":"high"},{"original_text":"Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing.","suggested_correction":"Students should only learn knowledge from books to do well in exams and not waste time on skills like cooking or dressing.","error_type":"grammar","explanation":"Missing plural form for 'exam' and 'skill'; 'skills' should be plural to match 'cooking or dressing'.","severity":"high"},{"original_text":"and not waste time on skill like cooking or dressing.","suggested_correction":"and not waste time on skills such as cooking or dressing.","error_type":"vocabulary","explanation":"'such as' is more appropriate than 'like' in formal writing.","severity":"medium"},{"original_text":"Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing.","suggested_correction":"Students should focus on learning knowledge from books to excel in exams rather than wasting time on practical skills such as cooking or dressing.","error_type":"coherence","explanation":"The sentence structure is awkward and could be clearer. A more cohesive structure improves clarity.","severity":"medium"},{"original_text":"Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing.","suggested_correction":"Students should prioritize learning theoretical knowledge from books to perform well in exams instead of spending time on practical skills like cooking or dressing.","error_type":"task_response","explanation":"The response lacks a balanced discussion of both books and practical skills; it should present a more nuanced view.","severity":"high"}],"annotated_essay":"Many people believes [CORRECTION: believes - believe] that education is important [CORRECTION: important - more important] than ever. Students should only learn knowledge from books to do well in exam [CORRECTION: exam - exams], and not waste time on skill [CORRECTION: skill - skills] like cooking or dressing. [COMMENT: The essay lacks depth and fails to address both sides of the argument adequately. A more balanced approach is needed.]","metadata":{"task_type":"task2","word_count":31,"scored_at":"2025-07-17 23:34:48","essay_length":181}};
    const essay = "Many people believes that education is important than ever. Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing.";
    
    console.log('Testing highlighting with cleaned data:', result);
    
    if (result && result.highlighted_corrections && essay) {
        try {
            const highlighter = new EssayHighlighter('highlightingTest');
            highlighter.init(result, essay);
            
            // Show success message
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success mt-3';
            successDiv.innerHTML = '<i class="fas fa-check"></i> Highlighting test completed successfully!';
            document.getElementById('highlightingTest').appendChild(successDiv);
            
        } catch (error) {
            console.error('Highlighting failed:', error);
            document.getElementById('highlightingTest').innerHTML = 
                '<div class="alert alert-danger"><i class="fas fa-times"></i> Highlighting failed: ' + error.message + '</div>';
        }
    } else {
        document.getElementById('highlightingTest').innerHTML = 
            '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> No valid data for highlighting test</div>';
    }
}
</script>
</body>
</html>
