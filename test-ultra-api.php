<?php
// Test ultra cleaning API directly

echo "<!DOCTYPE html>
<html>
<head>
    <title>Ultra API Cleaning Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
        .correction-clean { background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .correction-dirty { background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>🧪 Ultra API Cleaning Test</h1>";

// Test data
$testData = [
    'essay' => 'Many people believes that education is important than ever. Students should only learn knowledge from books.',
    'task_type' => 'task2',
    'prompt' => 'Discuss the importance of education.'
];

echo "<div class='info'>";
echo "<h3>Test Data:</h3>";
echo "<strong>Essay:</strong> " . htmlspecialchars($testData['essay']) . "<br>";
echo "<strong>Task Type:</strong> " . htmlspecialchars($testData['task_type']) . "<br>";
echo "<strong>Prompt:</strong> " . htmlspecialchars($testData['prompt']);
echo "</div>";

try {
    echo "<div class='warning'>🚀 Testing Ultra Cleaning API...</div>";
    
    // Call API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/ielts/api.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    if ($curlError) {
        throw new Exception('cURL Error: ' . $curlError);
    }
    
    if ($httpCode !== 200) {
        throw new Exception('HTTP Error: ' . $httpCode . ' - ' . $response);
    }
    
    $result = json_decode($response, true);
    if (!$result) {
        throw new Exception('Invalid JSON response');
    }
    
    echo "<div class='success'>✅ API call successful!</div>";
    
    // Analyze results
    if (isset($result['error']) && $result['error']) {
        echo "<div class='error'>";
        echo "<strong>API Error:</strong> " . htmlspecialchars($result['message']);
        echo "</div>";
    } else {
        echo "<div class='info'>";
        echo "<strong>Overall Band Score:</strong> " . ($result['overall_band_score'] ?? 'N/A') . "<br>";
        if (isset($result['cleaning_stats'])) {
            $stats = $result['cleaning_stats'];
            echo "<strong>Cleaning Stats:</strong><br>";
            echo "- Original corrections: " . $stats['original_count'] . "<br>";
            echo "- Clean corrections: " . $stats['cleaned_count'] . "<br>";
            echo "- Success rate: " . $stats['cleaning_success_rate'] . "%<br>";
        }
        echo "</div>";
        
        // Analyze corrections
        if (isset($result['highlighted_corrections']) && is_array($result['highlighted_corrections'])) {
            $corrections = $result['highlighted_corrections'];
            echo "<h3>🔍 Correction Analysis</h3>";
            echo "<p>Found " . count($corrections) . " corrections after ultra cleaning:</p>";
            
            $perfectCount = 0;
            $problematicCount = 0;
            
            foreach ($corrections as $i => $correction) {
                $originalText = $correction['original_text'] ?? '';
                $suggestedText = $correction['suggested_correction'] ?? '';
                
                // Ultra strict validation
                $isPerfect = (
                    !empty($originalText) &&
                    strlen($originalText) > 2 &&
                    strpos($originalText, 'data-') === false &&
                    strpos($originalText, '<') === false &&
                    strpos($originalText, '>') === false &&
                    strpos($originalText, '=') === false &&
                    strpos($originalText, 'correction-') === false &&
                    strpos($suggestedText, 'data-') === false &&
                    strpos($suggestedText, '<') === false
                );
                
                if ($isPerfect) {
                    $perfectCount++;
                    echo "<div class='correction-clean'>";
                    echo "<strong>✅ Perfect Correction " . ($i + 1) . ":</strong><br>";
                    echo "<strong>Original:</strong> \"" . htmlspecialchars($originalText) . "\"<br>";
                    echo "<strong>Suggested:</strong> \"" . htmlspecialchars($suggestedText) . "\"<br>";
                    echo "<strong>Type:</strong> " . htmlspecialchars($correction['error_type'] ?? 'N/A') . "<br>";
                    echo "</div>";
                } else {
                    $problematicCount++;
                    echo "<div class='correction-dirty'>";
                    echo "<strong>❌ Problematic Correction " . ($i + 1) . ":</strong><br>";
                    echo "<strong>Original:</strong> \"" . htmlspecialchars($originalText) . "\"<br>";
                    echo "<strong>Issues:</strong> ";
                    if (strpos($originalText, 'data-') !== false) echo "data-attributes ";
                    if (strpos($originalText, '<') !== false) echo "HTML-tags ";
                    if (strpos($originalText, '=') !== false) echo "equals-signs ";
                    if (empty($originalText)) echo "empty-text ";
                    echo "</div>";
                }
            }
            
            // Final verdict
            echo "<h3>🎯 Final Verdict</h3>";
            if ($perfectCount === count($corrections) && $perfectCount > 0) {
                echo "<div class='success'>";
                echo "<h4>🏆 ULTRA CLEANING SUCCESS!</h4>";
                echo "<p><strong>ALL {$perfectCount} corrections are perfectly clean!</strong></p>";
                echo "<p>✅ Ready for highlighting system</p>";
                echo "<p>✅ No HTML artifacts detected</p>";
                echo "<p>✅ Client-side validation will pass</p>";
                echo "</div>";
            } else if ($perfectCount > 0) {
                echo "<div class='warning'>";
                echo "<h4>⚠️ Partial Success</h4>";
                echo "<p><strong>{$perfectCount}</strong> perfect corrections, <strong>{$problematicCount}</strong> still problematic</p>";
                echo "</div>";
            } else {
                echo "<div class='error'>";
                echo "<h4>❌ Ultra Cleaning Failed</h4>";
                echo "<p>No clean corrections found. API cleaning needs improvement.</p>";
                echo "</div>";
            }
        } else {
            echo "<div class='warning'>No corrections found in response</div>";
        }
    }
    
    echo "<details>";
    echo "<summary>📋 Full API Response</summary>";
    echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>";
    echo "</details>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<strong>❌ Test Failed:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</body></html>";
?>
