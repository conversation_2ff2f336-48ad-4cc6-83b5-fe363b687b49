<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IELTS Writing Scorer - Advanced Professional Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #667eea;
            --secondary: #764ba2;
            --success: #27ae60;
            --danger: #e74c3c;
            --warning: #f39c12;
            --info: #3498db;
            --dark: #2c3e50;
            --light: #ecf0f1;
            --gradient-primary: linear-gradient(135deg, var(--primary), var(--secondary));
            --gradient-success: linear-gradient(135deg, #11998e, #38ef7d);
            --gradient-danger: linear-gradient(135deg, #ff6b6b, #ee5a24);
            --gradient-warning: linear-gradient(135deg, #f093fb, #f5576c);
            --gradient-info: linear-gradient(135deg, #4facfe, #00f2fe);
            --shadow-soft: 0 10px 40px rgba(0,0,0,0.1);
            --shadow-medium: 0 15px 50px rgba(0,0,0,0.15);
            --shadow-strong: 0 20px 60px rgba(0,0,0,0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            color: var(--dark);
            overflow-x: hidden;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: var(--shadow-strong);
            margin: 20px auto;
            max-width: 1600px;
            overflow: hidden;
            position: relative;
        }

        .header-section {
            background: var(--gradient-primary);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.2);
            letter-spacing: -1px;
        }

        .header-subtitle {
            font-size: 1.3rem;
            opacity: 0.95;
            font-weight: 400;
            max-width: 600px;
            margin: 0 auto;
        }

        .content-section {
            padding: 50px;
        }

        .score-dashboard {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 40px;
            margin-bottom: 50px;
        }

        .overall-score-card {
            background: var(--gradient-success);
            color: white;
            border-radius: 24px;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-medium);
        }

        .overall-score-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="80" r="2" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="1.5" fill="white" opacity="0.1"/></svg>');
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .score-number {
            font-size: 6rem;
            font-weight: 900;
            margin-bottom: 15px;
            text-shadow: 0 6px 12px rgba(0,0,0,0.3);
            position: relative;
            z-index: 2;
        }

        .score-label {
            font-size: 1.6rem;
            font-weight: 600;
            opacity: 0.95;
            position: relative;
            z-index: 2;
        }

        .score-description {
            font-size: 1rem;
            opacity: 0.9;
            margin-top: 15px;
            position: relative;
            z-index: 2;
        }

        .criteria-breakdown {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--shadow-soft);
        }

        .criteria-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .criteria-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .criteria-item {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            border-left: 4px solid var(--info);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .criteria-item:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-soft);
            border-left-color: var(--primary);
        }

        .criteria-item::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 50%;
            transform: translate(20px, -20px);
        }

        .criteria-name {
            font-size: 0.95rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 8px;
        }

        .criteria-score {
            font-size: 2.2rem;
            font-weight: 800;
            background: var(--gradient-info);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .criteria-feedback {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .analysis-section {
            display: grid;
            grid-template-columns: 1.5fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .essay-analysis {
            background: white;
            border-radius: 20px;
            padding: 35px;
            box-shadow: var(--shadow-soft);
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .view-toggle {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 4px;
        }

        .toggle-btn {
            background: none;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-btn.active {
            background: var(--primary);
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .essay-content {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 30px;
            line-height: 1.8;
            font-size: 1.05rem;
            border: 2px solid #e9ecef;
            min-height: 400px;
        }

        .corrections-panel {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--shadow-soft);
            max-height: 600px;
            overflow-y: auto;
        }

        .correction-card {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            border-left: 4px solid var(--warning);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .correction-card:hover {
            transform: translateX(8px);
            box-shadow: var(--shadow-soft);
            background: white;
        }

        .correction-card.active {
            border-left-color: var(--primary);
            background: white;
            box-shadow: var(--shadow-soft);
            transform: translateX(8px);
        }

        .correction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .correction-number {
            background: var(--gradient-warning);
            color: white;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.9rem;
        }

        .severity-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .severity-high {
            background: var(--gradient-danger);
            color: white;
        }

        .severity-medium {
            background: var(--gradient-warning);
            color: white;
        }

        .severity-low {
            background: var(--gradient-info);
            color: white;
        }

        .text-comparison {
            margin-bottom: 15px;
        }

        .original-text, .suggested-text {
            padding: 12px 16px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 8px 0;
            position: relative;
        }

        .original-text {
            background: linear-gradient(135deg, #ffe6e6, #ffcccc);
            border-left: 4px solid var(--danger);
            color: #c0392b;
        }

        .suggested-text {
            background: linear-gradient(135deg, #e6ffe6, #ccffcc);
            border-left: 4px solid var(--success);
            color: #27ae60;
        }

        .correction-explanation {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            padding: 15px;
            border-radius: 12px;
            font-size: 0.9rem;
            color: var(--dark);
            border-left: 4px solid var(--info);
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            box-shadow: var(--shadow-soft);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .stat-icon {
            font-size: 2.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .demo-watermark {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--gradient-warning);
            color: white;
            padding: 12px 24px;
            border-radius: 30px;
            font-weight: 700;
            box-shadow: var(--shadow-medium);
            z-index: 1000;
            animation: pulse 2s infinite;
        }

        .floating-actions {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 1000;
        }

        .fab {
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 50%;
            width: 56px;
            height: 56px;
            font-size: 1.4rem;
            box-shadow: var(--shadow-medium);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-strong);
        }

        .highlight-error {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2));
            padding: 2px 6px;
            border-radius: 6px;
            border-bottom: 2px solid var(--danger);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: inline-block;
        }

        .highlight-error:hover {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.3), rgba(192, 57, 43, 0.3));
            transform: scale(1.02);
        }

        .highlight-error.corrected {
            background: linear-gradient(135deg, rgba(39, 174, 96, 0.2), rgba(46, 204, 113, 0.2));
            border-bottom: 2px solid var(--success);
            color: var(--success);
            font-weight: 600;
        }

        .correction-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 300px;
            max-width: 400px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .correction-tooltip.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(-5px);
        }

        .correction-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 8px solid transparent;
            border-top-color: white;
        }

        .tooltip-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }

        .tooltip-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .tooltip-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tooltip-content {
            margin-bottom: 12px;
        }

        .tooltip-original {
            background: linear-gradient(135deg, #ffe6e6, #ffcccc);
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.85rem;
            margin-bottom: 8px;
            border-left: 3px solid var(--danger);
        }

        .tooltip-suggested {
            background: linear-gradient(135deg, #e6ffe6, #ccffcc);
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.85rem;
            margin-bottom: 8px;
            border-left: 3px solid var(--success);
        }

        .tooltip-explanation {
            font-size: 0.8rem;
            color: #6c757d;
            font-style: italic;
            margin-bottom: 12px;
        }

        .tooltip-actions {
            display: flex;
            gap: 8px;
        }

        .tooltip-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-accept {
            background: var(--gradient-success);
            color: white;
        }

        .btn-accept:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
        }

        .btn-dismiss {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #e9ecef;
        }

        .btn-dismiss:hover {
            background: #e9ecef;
        }

        .correction-accepted {
            background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1));
            border: 1px solid rgba(39, 174, 96, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
            animation: acceptedFlash 0.6s ease-out;
        }

        @keyframes acceptedFlash {
            0% { background: rgba(39, 174, 96, 0.3); }
            100% { background: rgba(39, 174, 96, 0.1); }
        }

        .progress-indicator {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border-radius: 25px;
            padding: 10px 20px;
            box-shadow: var(--shadow-medium);
            z-index: 1001;
            display: none;
        }

        .progress-text {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 5px;
        }

        .progress-bar {
            width: 200px;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--gradient-success);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        @media (max-width: 1200px) {
            .score-dashboard { grid-template-columns: 1fr; }
            .analysis-section { grid-template-columns: 1fr; }
            .criteria-grid { grid-template-columns: 1fr; }
        }

        .essay-form-section {
            margin-bottom: 50px;
        }

        .form-card, .tips-card {
            background: white;
            border-radius: 20px;
            padding: 35px;
            box-shadow: var(--shadow-soft);
            margin-bottom: 30px;
        }

        .tips-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .tips-list {
            list-style: none;
            padding: 0;
        }

        .tips-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.95rem;
        }

        .criteria-mini {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
        }

        .criteria-item-mini {
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }

        .criteria-item-mini:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }

        .criteria-item-mini strong {
            display: block;
            font-size: 0.9rem;
            color: var(--dark);
        }

        .criteria-item-mini small {
            color: #6c757d;
            font-size: 0.8rem;
        }

        .loading-section {
            text-align: center;
            padding: 80px 20px;
        }

        .loading-card {
            background: white;
            border-radius: 24px;
            padding: 50px;
            box-shadow: var(--shadow-medium);
            max-width: 600px;
            margin: 0 auto;
        }

        .loading-animation {
            margin-bottom: 30px;
        }

        .spinner {
            width: 80px;
            height: 80px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 15px;
        }

        .loading-text {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 40px;
        }

        .progress-bar-loading {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 30px;
        }

        .progress-fill-loading {
            height: 100%;
            background: var(--gradient-primary);
            border-radius: 4px;
            width: 0%;
            animation: loadingProgress 8s ease-in-out forwards;
        }

        @keyframes loadingProgress {
            0% { width: 0%; }
            25% { width: 25%; }
            50% { width: 50%; }
            75% { width: 75%; }
            100% { width: 100%; }
        }

        .loading-steps {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }

        .step {
            flex: 1;
            text-align: center;
            padding: 15px;
            border-radius: 12px;
            background: #f8f9fa;
            color: #6c757d;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .step.active {
            background: var(--gradient-primary);
            color: white;
            transform: scale(1.05);
        }

        .step i {
            display: block;
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .results-section {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        #wordCount {
            font-weight: 600;
            color: var(--primary);
        }

        @media (max-width: 768px) {
            .header-title { font-size: 2.5rem; }
            .score-number { font-size: 4rem; }
            .content-section { padding: 30px 20px; }
            .stats-overview { grid-template-columns: repeat(2, 1fr); }
            .loading-steps { flex-direction: column; gap: 10px; }
            .form-card, .tips-card { padding: 25px; }
        }
    </style>
</head>
<body>
    <!-- Demo Watermark -->
    <div class="demo-watermark animate__animated animate__pulse animate__infinite">
        <i class="fas fa-flask"></i> DEMO MODE
    </div>

    <!-- Progress Indicator -->
    <div class="progress-indicator" id="progressIndicator">
        <div class="progress-text" id="progressText">Corrections Applied: 0/5</div>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
    </div>

    <!-- Floating Actions -->
    <div class="floating-actions">
        <button class="fab" onclick="newEssay()" title="Submit new essay" style="background: var(--gradient-success);">
            <i class="fas fa-plus"></i>
        </button>
        <button class="fab" onclick="resetCorrections()" title="Reset corrections" style="background: var(--gradient-warning);">
            <i class="fas fa-undo"></i>
        </button>
        <button class="fab" onclick="scrollToTop()" title="Scroll to top">
            <i class="fas fa-arrow-up"></i>
        </button>
        <button class="fab" onclick="toggleFullscreen()" title="Toggle fullscreen">
            <i class="fas fa-expand"></i>
        </button>
        <button class="fab" onclick="exportReport()" title="Export report">
            <i class="fas fa-download"></i>
        </button>
    </div>

    <div class="container-fluid">
        <div class="main-container animate__animated animate__fadeIn">
            <!-- Header Section -->
            <div class="header-section">
                <div class="header-content">
                    <h1 class="header-title animate__animated animate__fadeInDown">
                        <i class="fas fa-graduation-cap"></i>
                        IELTS Writing Scorer
                    </h1>
                    <p class="header-subtitle animate__animated animate__fadeInUp animate__delay-1s">
                        Advanced AI-Powered Assessment with Professional Feedback & Detailed Analysis
                    </p>
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <!-- Essay Submission Form -->
                <div class="essay-form-section" id="essayFormSection">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="form-card animate__animated animate__fadeInLeft">
                                <h3 class="section-title">
                                    <i class="fas fa-edit"></i>
                                    Submit Your Essay for Scoring
                                </h3>
                                <form id="essaySubmissionForm">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="taskType" class="form-label">Task Type</label>
                                            <select class="form-select" id="taskType" required>
                                                <option value="">Select task type...</option>
                                                <option value="task2">Task 2 (Essay)</option>
                                                <option value="task1_academic">Task 1 Academic</option>
                                                <option value="task1_general">Task 1 General</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="prompt" class="form-label">Essay Prompt</label>
                                            <input type="text" class="form-control" id="prompt"
                                                   placeholder="Enter the essay question/prompt..."
                                                   value="Discuss the importance of education in modern society.">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="essayText" class="form-label">Your Essay</label>
                                        <textarea class="form-control" id="essayText" rows="12"
                                                  placeholder="Write your essay here..." required></textarea>
                                        <div class="form-text">
                                            <span id="wordCount">0</span> words | Minimum 250 words recommended for Task 2
                                        </div>
                                    </div>

                                    <div class="d-flex gap-3">
                                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                            <i class="fas fa-paper-plane"></i> Score My Essay
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-lg" onclick="loadDemoEssay()">
                                            <i class="fas fa-flask"></i> Load Demo Essay
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-lg" onclick="clearForm()">
                                            <i class="fas fa-trash"></i> Clear
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="tips-card animate__animated animate__fadeInRight">
                                <h4 class="tips-title">
                                    <i class="fas fa-lightbulb"></i>
                                    Writing Tips
                                </h4>
                                <ul class="tips-list">
                                    <li><i class="fas fa-check text-success"></i> Write at least 250 words for Task 2</li>
                                    <li><i class="fas fa-check text-success"></i> Include clear introduction and conclusion</li>
                                    <li><i class="fas fa-check text-success"></i> Use formal academic language</li>
                                    <li><i class="fas fa-check text-success"></i> Support ideas with examples</li>
                                    <li><i class="fas fa-check text-success"></i> Check grammar and spelling</li>
                                    <li><i class="fas fa-check text-success"></i> Manage your time effectively</li>
                                </ul>

                                <div class="scoring-info mt-4">
                                    <h5><i class="fas fa-star"></i> Scoring Criteria</h5>
                                    <div class="criteria-mini">
                                        <div class="criteria-item-mini">
                                            <strong>Task Achievement</strong>
                                            <small>Address all parts of the task</small>
                                        </div>
                                        <div class="criteria-item-mini">
                                            <strong>Coherence & Cohesion</strong>
                                            <small>Logical organization and linking</small>
                                        </div>
                                        <div class="criteria-item-mini">
                                            <strong>Lexical Resource</strong>
                                            <small>Vocabulary range and accuracy</small>
                                        </div>
                                        <div class="criteria-item-mini">
                                            <strong>Grammar Range & Accuracy</strong>
                                            <small>Sentence structures and accuracy</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading Section -->
                <div class="loading-section" id="loadingSection" style="display: none;">
                    <div class="loading-card">
                        <div class="loading-animation">
                            <div class="spinner"></div>
                        </div>
                        <h3 class="loading-title">Analyzing Your Essay...</h3>
                        <p class="loading-text">Our AI is carefully reviewing your writing for grammar, vocabulary, coherence, and task achievement.</p>
                        <div class="loading-progress">
                            <div class="progress-bar-loading">
                                <div class="progress-fill-loading"></div>
                            </div>
                            <div class="loading-steps">
                                <div class="step active" id="step1">
                                    <i class="fas fa-file-text"></i> Processing text
                                </div>
                                <div class="step" id="step2">
                                    <i class="fas fa-search"></i> Analyzing errors
                                </div>
                                <div class="step" id="step3">
                                    <i class="fas fa-chart-bar"></i> Calculating scores
                                </div>
                                <div class="step" id="step4">
                                    <i class="fas fa-check"></i> Generating report
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Section -->
                <div class="results-section" id="resultsSection" style="display: none;">
                <!-- Score Dashboard -->
                <div class="score-dashboard">
                    <div class="overall-score-card animate__animated animate__zoomIn">
                        <div class="score-number" id="overallScore">5.5</div>
                        <div class="score-label">Overall Band Score</div>
                        <div class="score-description">
                            Competent User - Good command of the language despite some inaccuracies
                        </div>
                    </div>
                    
                    <div class="criteria-breakdown animate__animated animate__fadeInRight">
                        <h3 class="criteria-title">
                            <i class="fas fa-chart-bar"></i>
                            Detailed Breakdown
                        </h3>
                        <div class="criteria-grid">
                            <div class="criteria-item">
                                <div class="criteria-name">Task Achievement</div>
                                <div class="criteria-score">5.5</div>
                                <div class="criteria-feedback">Addresses task with some clarity</div>
                            </div>
                            <div class="criteria-item">
                                <div class="criteria-name">Coherence & Cohesion</div>
                                <div class="criteria-score">5.0</div>
                                <div class="criteria-feedback">Basic organization with limited linking</div>
                            </div>
                            <div class="criteria-item">
                                <div class="criteria-name">Lexical Resource</div>
                                <div class="criteria-score">5.5</div>
                                <div class="criteria-feedback">Adequate vocabulary with some errors</div>
                            </div>
                            <div class="criteria-item">
                                <div class="criteria-name">Grammar Range & Accuracy</div>
                                <div class="criteria-score">5.0</div>
                                <div class="criteria-feedback">Limited range with frequent errors</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analysis Section -->
                <div class="analysis-section">
                    <div class="essay-analysis animate__animated animate__fadeInLeft">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-file-alt"></i>
                                Essay Analysis
                            </h3>
                            <div class="view-toggle">
                                <button class="toggle-btn active" onclick="toggleView('highlighted')">
                                    <i class="fas fa-highlighter"></i> Highlighted
                                </button>
                                <button class="toggle-btn" onclick="toggleView('clean')">
                                    <i class="fas fa-file-text"></i> Clean
                                </button>
                            </div>
                        </div>
                        <div class="essay-content" id="essayContent">
                            <!-- Essay content will be populated here -->
                        </div>
                    </div>
                    
                    <div class="corrections-panel animate__animated animate__fadeInRight">
                        <h3 class="section-title">
                            <i class="fas fa-exclamation-triangle"></i>
                            Key Corrections
                            <span class="badge bg-danger ms-2">5</span>
                        </h3>
                        <div id="correctionsContainer">
                            <!-- Corrections will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Statistics Overview -->
                <div class="stats-overview">
                    <div class="stat-card animate__animated animate__fadeInUp">
                        <div class="stat-icon">
                            <i class="fas fa-file-word"></i>
                        </div>
                        <div class="stat-number">202</div>
                        <div class="stat-label">Word Count</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-1s">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="stat-number">5</div>
                        <div class="stat-label">Corrections</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-2s">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Clean Rate</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-3s">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">12.9s</div>
                        <div class="stat-label">Process Time</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-4s">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-number">B2</div>
                        <div class="stat-label">CEFR Level</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-5s">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-number">68%</div>
                        <div class="stat-label">Accuracy</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Demo data
        const demoData = {
            "overall_band_score": 5.5,
            "highlighted_corrections": [
                {
                    "original_text": "Many peoples thinking that student must learn only brainy subjects",
                    "suggested_correction": "Many people think that students must learn only academic subjects",
                    "error_type": "grammar|vocabulary",
                    "explanation": "Incorrect plural form and verb tense; 'brainy' is informal.",
                    "severity": "high"
                },
                {
                    "original_text": "I am disagree totality.",
                    "suggested_correction": "I totally disagree.",
                    "error_type": "grammar",
                    "explanation": "Incorrect verb form and word order.",
                    "severity": "high"
                },
                {
                    "original_text": "school is not only for put information into children brain",
                    "suggested_correction": "school is not only for putting information into children's brains",
                    "error_type": "grammar",
                    "explanation": "Incorrect verb form and possessive form.",
                    "severity": "high"
                },
                {
                    "original_text": "When going to job finding, people should wearing nice clothes",
                    "suggested_correction": "When looking for a job, people should wear nice clothes",
                    "error_type": "grammar",
                    "explanation": "Incorrect verb form and phrasing.",
                    "severity": "high"
                },
                {
                    "original_text": "If student cannot cook, they will hungry",
                    "suggested_correction": "If students cannot cook, they will be hungry",
                    "error_type": "grammar",
                    "explanation": "Subject-verb agreement and missing verb.",
                    "severity": "high"
                }
            ]
        };

        const sampleEssay = `Nowadays, education is the golden key for students to open their success door. Many peoples thinking that student must learn only brainy subjects like math and chemical to becoming big guy in society. Other skill like making food or wearing cloth is useless and spend time. I am disagree totality.

In my opinion, school is not only for put information into children brain, but also for create them into good person and survive in jungle life. If student cannot cook, they will hungry and go to restaurant always, which is costly and non-healthy.

Moreover, dressing is not small matter. When going to job finding, people should wearing nice clothes to make love impression on manager. If student look messy, they maybe not get chance to job even they have big brain.

On the contradiction, I understand why people think book knowledge is more taller. Academic knowledge help in passing exam and become intelligence. But life is not only exam, it's full of many obstacle like living alone or wearing properly.

In finally, I strong believe that mixing academic and daily life skill is the best idea. Children should learn both book thing and survival tactics to becoming success in their journey.`;

        let currentView = 'highlighted';
        let activeCorrection = -1;
        let correctedTexts = new Set(); // Track which corrections have been accepted
        let activeTooltip = null;
        let currentEssayData = null; // Store current essay data
        let isDemo = true; // Track if we're in demo mode

        function initializeDemo() {
            populateEssay();
            populateCorrections();
            animateScore();
        }

        function populateEssay() {
            const container = document.getElementById('essayContent');
            const essayData = currentEssayData || demoData;
            const corrections = essayData.highlighted_corrections || [];

            if (currentView === 'highlighted') {
                container.innerHTML = formatEssayWithHighlights(sampleEssay, corrections);
            } else {
                container.innerHTML = sampleEssay.replace(/\n/g, '<br><br>');
            }
        }

        function formatEssayWithHighlights(essay, corrections) {
            // Start fresh with the original essay
            let processedEssay = essay;

            // Process corrections in reverse order to maintain text positions
            const sortedCorrections = corrections.map((correction, index) => ({
                ...correction,
                originalIndex: index
            })).sort((a, b) => {
                // Sort by the position of text in essay (last occurrence first)
                const posA = processedEssay.lastIndexOf(a.original_text);
                const posB = processedEssay.lastIndexOf(b.original_text);
                return posB - posA;
            });

            sortedCorrections.forEach((correction) => {
                const index = correction.originalIndex;
                const originalText = correction.original_text;
                const isAccepted = correctedTexts.has(index);
                const displayText = isAccepted ? correction.suggested_correction : originalText;
                const cssClass = isAccepted ? 'highlight-error corrected' : 'highlight-error';

                // Create the replacement HTML
                const replacement = `<span class="${cssClass}" data-correction-index="${index}" onmouseenter="showCorrectionTooltip(event, ${index})" onmouseleave="hideCorrectionTooltip()" onclick="selectCorrection(${index})">${displayText}</span>`;

                // Replace only the first occurrence to avoid conflicts
                const position = processedEssay.indexOf(originalText);
                if (position !== -1) {
                    processedEssay = processedEssay.substring(0, position) +
                                   replacement +
                                   processedEssay.substring(position + originalText.length);
                }
            });

            return processedEssay.replace(/\n/g, '<br><br>');
        }

        function populateCorrections() {
            const container = document.getElementById('correctionsContainer');
            const essayData = currentEssayData || demoData;
            const corrections = essayData.highlighted_corrections || [];

            container.innerHTML = corrections.map((correction, index) => {
                const isAccepted = correctedTexts.has(index);
                const cardClass = `correction-card ${index === activeCorrection ? 'active' : ''} ${isAccepted ? 'corrected' : ''}`;

                return `
                <div class="${cardClass}" onclick="selectCorrection(${index})">
                    <div class="correction-header">
                        <div class="correction-number" style="${isAccepted ? 'background: var(--gradient-success);' : ''}">${index + 1}</div>
                        <div style="display: flex; gap: 8px; align-items: center;">
                            ${isAccepted ? '<span class="badge bg-success"><i class="fas fa-check"></i> Applied</span>' : ''}
                            <span class="severity-badge severity-${correction.severity}">
                                <i class="fas fa-${getSeverityIcon(correction.severity)}"></i>
                                ${correction.severity}
                            </span>
                        </div>
                    </div>
                    <div class="text-comparison">
                        <div class="original-text">
                            <strong><i class="fas fa-times"></i> Original:</strong><br>
                            "${correction.original_text}"
                        </div>
                        <div class="suggested-text">
                            <strong><i class="fas fa-check"></i> Suggested:</strong><br>
                            "${correction.suggested_correction}"
                        </div>
                    </div>
                    <div class="correction-explanation">
                        <i class="fas fa-lightbulb"></i> ${correction.explanation}
                    </div>
                    ${!isAccepted ? `
                        <div class="mt-2 text-center">
                            <button class="btn btn-sm btn-success" onclick="event.stopPropagation(); acceptCorrection(${index})">
                                <i class="fas fa-check"></i> Apply Correction
                            </button>
                        </div>
                    ` : ''}
                </div>
            `;
            }).join('');
        }

        function selectCorrection(index) {
            activeCorrection = index;
            
            // Update corrections display
            populateCorrections();
            
            // Highlight in essay
            const highlights = document.querySelectorAll('.highlight-error');
            highlights.forEach((el, i) => {
                el.style.background = i === index ? 
                    'linear-gradient(135deg, rgba(231, 76, 60, 0.4), rgba(192, 57, 43, 0.4))' :
                    'linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2))';
            });
            
            if (highlights[index]) {
                highlights[index].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        function toggleView(view) {
            currentView = view;
            
            // Update toggle buttons
            document.querySelectorAll('.toggle-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update essay display
            populateEssay();
            
            // Reinitialize tooltips if highlighted view
            if (view === 'highlighted') {
                setTimeout(() => {
                    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    tooltipTriggerList.map(function (tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                }, 100);
            }
        }

        function getSeverityIcon(severity) {
            const icons = {
                'high': 'exclamation-triangle',
                'medium': 'exclamation-circle',
                'low': 'info-circle'
            };
            return icons[severity] || 'info-circle';
        }

        function animateScore() {
            const scoreElement = document.getElementById('overallScore');
            let current = 0;
            const target = 5.5;
            const increment = target / 100;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                scoreElement.textContent = current.toFixed(1);
            }, 20);
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        function exportReport() {
            // Simulate export functionality
            const toast = document.createElement('div');
            toast.className = 'toast-container position-fixed top-0 end-0 p-3';
            toast.innerHTML = `
                <div class="toast show" role="alert">
                    <div class="toast-header">
                        <i class="fas fa-download text-primary me-2"></i>
                        <strong class="me-auto">Export</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        Report exported successfully! (Demo mode)
                    </div>
                </div>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // Tooltip functions for corrections
        function showCorrectionTooltip(event, index) {
            if (correctedTexts.has(index)) return; // Don't show tooltip for already corrected text

            // Hide any existing tooltip
            hideCorrectionTooltip();

            const essayData = currentEssayData || demoData;
            const correction = essayData.highlighted_corrections[index];
            const element = event.target;

            // Create tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'correction-tooltip';
            tooltip.innerHTML = `
                <div class="tooltip-header">
                    <div class="tooltip-title">
                        <i class="fas fa-lightbulb"></i>
                        Correction Suggestion
                    </div>
                    <button class="tooltip-close" onclick="hideCorrectionTooltip()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="tooltip-content">
                    <div class="tooltip-original">
                        <strong><i class="fas fa-times text-danger"></i> Original:</strong><br>
                        "${correction.original_text}"
                    </div>
                    <div class="tooltip-suggested">
                        <strong><i class="fas fa-check text-success"></i> Suggested:</strong><br>
                        "${correction.suggested_correction}"
                    </div>
                    <div class="tooltip-explanation">
                        <i class="fas fa-info-circle"></i> ${correction.explanation}
                    </div>
                </div>
                <div class="tooltip-actions">
                    <button class="tooltip-btn btn-accept" onclick="acceptCorrection(${index})">
                        <i class="fas fa-check"></i> Accept
                    </button>
                    <button class="tooltip-btn btn-dismiss" onclick="hideCorrectionTooltip()">
                        <i class="fas fa-times"></i> Dismiss
                    </button>
                </div>
            `;

            // Position tooltip
            element.appendChild(tooltip);
            activeTooltip = tooltip;

            // Show tooltip with animation
            setTimeout(() => {
                tooltip.classList.add('show');
            }, 10);
        }

        function hideCorrectionTooltip() {
            if (activeTooltip) {
                activeTooltip.classList.remove('show');
                setTimeout(() => {
                    if (activeTooltip && activeTooltip.parentNode) {
                        activeTooltip.parentNode.removeChild(activeTooltip);
                    }
                    activeTooltip = null;
                }, 300);
            }
        }

        function acceptCorrection(index) {
            // Hide tooltip
            hideCorrectionTooltip();

            // Mark as corrected
            correctedTexts.add(index);

            // Update progress
            updateProgress();

            // Re-render essay with correction applied
            populateEssay();

            // Update corrections panel
            populateCorrections();

            // Show success animation
            showAcceptedFeedback(index);

            // Check if all corrections are accepted
            const essayData = currentEssayData || demoData;
            if (correctedTexts.size === (essayData.highlighted_corrections || []).length) {
                showCompletionCelebration();
            }
        }

        function updateProgress() {
            const essayData = currentEssayData || demoData;
            const total = (essayData.highlighted_corrections || []).length;
            const completed = correctedTexts.size;
            const percentage = total > 0 ? (completed / total) * 100 : 0;

            const progressIndicator = document.getElementById('progressIndicator');
            const progressText = document.getElementById('progressText');
            const progressFill = document.getElementById('progressFill');

            progressText.textContent = `Corrections Applied: ${completed}/${total}`;
            progressFill.style.width = `${percentage}%`;

            // Show progress indicator
            if (completed > 0) {
                progressIndicator.style.display = 'block';
            }
        }

        function showAcceptedFeedback(index) {
            const essayData = currentEssayData || demoData;
            const correction = essayData.highlighted_corrections[index];

            // Create floating success message
            const feedback = document.createElement('div');
            feedback.className = 'correction-accepted';
            feedback.innerHTML = `
                <i class="fas fa-check-circle text-success"></i>
                <strong>Correction Applied!</strong><br>
                <small>"${correction.original_text}" → "${correction.suggested_correction}"</small>
            `;

            // Add to essay container
            const essayContainer = document.getElementById('essayContent');
            essayContainer.appendChild(feedback);

            // Remove after animation
            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 3000);
        }

        function showCompletionCelebration() {
            // Create celebration modal
            const celebration = document.createElement('div');
            celebration.className = 'modal fade';
            celebration.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-body text-center p-5">
                            <div style="font-size: 4rem; color: #27ae60; margin-bottom: 20px;">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <h3 class="text-success mb-3">Congratulations! 🎉</h3>
                            <p class="lead">You have successfully applied all corrections!</p>
                            <p class="text-muted">Your essay is now improved and ready for review.</p>
                            <button class="btn btn-success btn-lg mt-3" data-bs-dismiss="modal">
                                <i class="fas fa-check"></i> Continue Learning
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(celebration);
            const modal = new bootstrap.Modal(celebration);
            modal.show();

            // Remove modal after closing
            celebration.addEventListener('hidden.bs.modal', () => {
                celebration.remove();
            });
        }

        function resetCorrections() {
            correctedTexts.clear();
            activeCorrection = -1;
            hideCorrectionTooltip();
            updateProgress();

            // Force clean re-render
            currentView = 'highlighted';
            populateEssay();
            populateCorrections();

            // Hide progress indicator
            document.getElementById('progressIndicator').style.display = 'none';

            // Show reset confirmation
            const toast = document.createElement('div');
            toast.className = 'toast-container position-fixed top-0 end-0 p-3';
            toast.innerHTML = `
                <div class="toast show" role="alert">
                    <div class="toast-header">
                        <i class="fas fa-undo text-warning me-2"></i>
                        <strong class="me-auto">Reset</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        All corrections have been reset. You can practice again!
                    </div>
                </div>
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // Form handling functions
        function initializeFormHandlers() {
            // Form submission handler
            document.getElementById('essaySubmissionForm').addEventListener('submit', handleFormSubmission);

            // Word count handler
            document.getElementById('essayText').addEventListener('input', updateWordCount);

            // Task type change handler
            document.getElementById('taskType').addEventListener('change', updatePromptPlaceholder);
        }

        function updateWordCount() {
            const text = document.getElementById('essayText').value;
            const wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
            document.getElementById('wordCount').textContent = wordCount;

            // Update color based on word count
            const wordCountElement = document.getElementById('wordCount');
            if (wordCount < 150) {
                wordCountElement.style.color = '#e74c3c';
            } else if (wordCount < 250) {
                wordCountElement.style.color = '#f39c12';
            } else {
                wordCountElement.style.color = '#27ae60';
            }
        }

        function updatePromptPlaceholder() {
            const taskType = document.getElementById('taskType').value;
            const promptInput = document.getElementById('prompt');

            const placeholders = {
                'task2': 'Some people believe that... Discuss both views and give your opinion.',
                'task1_academic': 'The chart shows... Summarize the information by selecting and reporting the main features.',
                'task1_general': 'You recently... Write a letter to...'
            };

            promptInput.placeholder = placeholders[taskType] || 'Enter the essay question/prompt...';
        }

        async function handleFormSubmission(event) {
            event.preventDefault();

            // Get form data
            const formData = {
                essay: document.getElementById('essayText').value.trim(),
                task_type: document.getElementById('taskType').value,
                prompt: document.getElementById('prompt').value.trim()
            };

            // Validate form
            if (!validateForm(formData)) {
                return;
            }

            // Show loading
            showLoadingSection();

            try {
                // Call API
                const result = await callScoringAPI(formData);

                // Store result
                currentEssayData = result;
                isDemo = false;

                // Show results
                showResultsSection(result, formData.essay);

            } catch (error) {
                console.error('Scoring error:', error);
                showErrorMessage(error.message);
                showFormSection();
            }
        }

        function validateForm(formData) {
            if (!formData.essay) {
                showErrorMessage('Please enter your essay text.');
                return false;
            }

            if (formData.essay.split(/\s+/).length < 50) {
                showErrorMessage('Essay is too short. Please write at least 50 words.');
                return false;
            }

            if (!formData.task_type) {
                showErrorMessage('Please select a task type.');
                return false;
            }

            return true;
        }

        async function callScoringAPI(formData) {
            const response = await fetch('api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.error) {
                throw new Error(result.message || 'API error occurred');
            }

            return result;
        }

        // UI Section Management
        function showFormSection() {
            document.getElementById('essayFormSection').style.display = 'block';
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'none';
        }

        function showLoadingSection() {
            document.getElementById('essayFormSection').style.display = 'none';
            document.getElementById('loadingSection').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';

            // Animate loading steps
            animateLoadingSteps();
        }

        function showResultsSection(result, originalEssay) {
            document.getElementById('essayFormSection').style.display = 'none';
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'block';

            // Reset corrections state
            correctedTexts.clear();
            activeCorrection = -1;

            // Update UI with new data
            updateScoreDisplay(result);
            updateEssayDisplay(originalEssay, result.highlighted_corrections || []);
            updateCorrectionsDisplay(result.highlighted_corrections || []);
            updateStatistics(result);

            // Scroll to results
            document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
        }

        function animateLoadingSteps() {
            const steps = ['step1', 'step2', 'step3', 'step4'];
            let currentStep = 0;

            const interval = setInterval(() => {
                // Remove active from all steps
                steps.forEach(step => {
                    document.getElementById(step).classList.remove('active');
                });

                // Add active to current step
                if (currentStep < steps.length) {
                    document.getElementById(steps[currentStep]).classList.add('active');
                    currentStep++;
                } else {
                    clearInterval(interval);
                }
            }, 2000);
        }

        function updateScoreDisplay(result) {
            // Update overall score
            const scoreElement = document.getElementById('overallScore');
            if (scoreElement) {
                animateNumberTo(scoreElement, result.overall_band_score || 0);
            }

            // Update criteria scores (if available)
            // This would need to be expanded based on your API response structure
        }

        function updateEssayDisplay(essay, corrections) {
            sampleEssay = essay; // Update global essay variable
            populateEssay();
        }

        function updateCorrectionsDisplay(corrections) {
            // Update global data
            if (currentEssayData) {
                currentEssayData.highlighted_corrections = corrections;
            }
            populateCorrections();
        }

        function updateStatistics(result) {
            // Update word count
            const metadata = result.request_metadata || result.metadata || {};
            const wordCount = metadata.word_count || 0;
            const processingTime = metadata.processing_time || 0;
            const correctionsCount = (result.highlighted_corrections || []).length;

            // Update stat cards if they exist
            const statCards = document.querySelectorAll('.stat-number');
            if (statCards.length >= 4) {
                statCards[0].textContent = wordCount;
                statCards[1].textContent = correctionsCount;
                statCards[2].textContent = '100%'; // Clean rate
                statCards[3].textContent = processingTime.toFixed(1) + 's';
            }
        }

        function animateNumberTo(element, targetValue) {
            let currentValue = 0;
            const increment = targetValue / 50;
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= targetValue) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                element.textContent = currentValue.toFixed(1);
            }, 20);
        }

        // Utility functions
        function loadDemoEssay() {
            document.getElementById('essayText').value = sampleEssay;
            document.getElementById('taskType').value = 'task2';
            document.getElementById('prompt').value = 'Discuss the importance of education in modern society.';
            updateWordCount();
        }

        function clearForm() {
            document.getElementById('essaySubmissionForm').reset();
            updateWordCount();
        }

        function showErrorMessage(message) {
            // Create error toast
            const toast = document.createElement('div');
            toast.className = 'toast-container position-fixed top-0 end-0 p-3';
            toast.innerHTML = `
                <div class="toast show" role="alert">
                    <div class="toast-header">
                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                        <strong class="me-auto">Error</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }

        function newEssay() {
            // Reset everything and show form
            correctedTexts.clear();
            activeCorrection = -1;
            currentEssayData = null;
            isDemo = true;
            hideCorrectionTooltip();
            showFormSection();
            clearForm();
        }

        // Initialize demo when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeDemo();
            initializeFormHandlers();
            showFormSection(); // Start with form visible
        });

        // Close tooltip when clicking outside
        document.addEventListener('click', function(event) {
            if (activeTooltip && !event.target.closest('.correction-tooltip') && !event.target.closest('.highlight-error')) {
                hideCorrectionTooltip();
            }
        });
    </script>
</body>
</html>
