<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IELTS Writing Scorer - Advanced Professional Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #667eea;
            --secondary: #764ba2;
            --success: #27ae60;
            --danger: #e74c3c;
            --warning: #f39c12;
            --info: #3498db;
            --dark: #2c3e50;
            --light: #ecf0f1;
            --gradient-primary: linear-gradient(135deg, var(--primary), var(--secondary));
            --gradient-success: linear-gradient(135deg, #11998e, #38ef7d);
            --gradient-danger: linear-gradient(135deg, #ff6b6b, #ee5a24);
            --gradient-warning: linear-gradient(135deg, #f093fb, #f5576c);
            --gradient-info: linear-gradient(135deg, #4facfe, #00f2fe);
            --shadow-soft: 0 10px 40px rgba(0,0,0,0.1);
            --shadow-medium: 0 15px 50px rgba(0,0,0,0.15);
            --shadow-strong: 0 20px 60px rgba(0,0,0,0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            color: var(--dark);
            overflow-x: hidden;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: var(--shadow-strong);
            margin: 20px auto;
            max-width: 1600px;
            overflow: hidden;
            position: relative;
        }

        .header-section {
            background: var(--gradient-primary);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.2);
            letter-spacing: -1px;
        }

        .header-subtitle {
            font-size: 1.3rem;
            opacity: 0.95;
            font-weight: 400;
            max-width: 600px;
            margin: 0 auto;
        }

        .content-section {
            padding: 50px;
        }

        .score-dashboard {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 40px;
            margin-bottom: 50px;
        }

        .overall-score-card {
            background: var(--gradient-success);
            color: white;
            border-radius: 24px;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-medium);
        }

        .overall-score-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="80" r="2" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="1.5" fill="white" opacity="0.1"/></svg>');
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .score-number {
            font-size: 6rem;
            font-weight: 900;
            margin-bottom: 15px;
            text-shadow: 0 6px 12px rgba(0,0,0,0.3);
            position: relative;
            z-index: 2;
        }

        .score-label {
            font-size: 1.6rem;
            font-weight: 600;
            opacity: 0.95;
            position: relative;
            z-index: 2;
        }

        .score-description {
            font-size: 1rem;
            opacity: 0.9;
            margin-top: 15px;
            position: relative;
            z-index: 2;
        }

        .criteria-breakdown {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--shadow-soft);
        }

        .criteria-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .criteria-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .criteria-item {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            border-left: 4px solid var(--info);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .criteria-item:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-soft);
            border-left-color: var(--primary);
        }

        .criteria-item::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 50%;
            transform: translate(20px, -20px);
        }

        .criteria-name {
            font-size: 0.95rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 8px;
        }

        .criteria-score {
            font-size: 2.2rem;
            font-weight: 800;
            background: var(--gradient-info);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .criteria-feedback {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .analysis-section {
            display: grid;
            grid-template-columns: 1.5fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .essay-analysis {
            background: white;
            border-radius: 20px;
            padding: 35px;
            box-shadow: var(--shadow-soft);
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .view-toggle {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 4px;
        }

        .toggle-btn {
            background: none;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-btn.active {
            background: var(--primary);
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .essay-content {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 30px;
            line-height: 1.8;
            font-size: 1.05rem;
            border: 2px solid #e9ecef;
            min-height: 400px;
        }

        .corrections-panel {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--shadow-soft);
            max-height: 600px;
            overflow-y: auto;
        }

        .correction-card {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            border-left: 4px solid var(--warning);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .correction-card:hover {
            transform: translateX(8px);
            box-shadow: var(--shadow-soft);
            background: white;
        }

        .correction-card.active {
            border-left-color: var(--primary);
            background: white;
            box-shadow: var(--shadow-soft);
            transform: translateX(8px);
        }

        .correction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .correction-number {
            background: var(--gradient-warning);
            color: white;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.9rem;
        }

        .severity-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .severity-high {
            background: var(--gradient-danger);
            color: white;
        }

        .severity-medium {
            background: var(--gradient-warning);
            color: white;
        }

        .severity-low {
            background: var(--gradient-info);
            color: white;
        }

        .text-comparison {
            margin-bottom: 15px;
        }

        .original-text, .suggested-text {
            padding: 12px 16px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 8px 0;
            position: relative;
        }

        .original-text {
            background: linear-gradient(135deg, #ffe6e6, #ffcccc);
            border-left: 4px solid var(--danger);
            color: #c0392b;
        }

        .suggested-text {
            background: linear-gradient(135deg, #e6ffe6, #ccffcc);
            border-left: 4px solid var(--success);
            color: #27ae60;
        }

        .correction-explanation {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            padding: 15px;
            border-radius: 12px;
            font-size: 0.9rem;
            color: var(--dark);
            border-left: 4px solid var(--info);
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            box-shadow: var(--shadow-soft);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .stat-icon {
            font-size: 2.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .demo-watermark {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--gradient-warning);
            color: white;
            padding: 12px 24px;
            border-radius: 30px;
            font-weight: 700;
            box-shadow: var(--shadow-medium);
            z-index: 1000;
            animation: pulse 2s infinite;
        }

        .floating-actions {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 1000;
        }

        .fab {
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 50%;
            width: 56px;
            height: 56px;
            font-size: 1.4rem;
            box-shadow: var(--shadow-medium);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-strong);
        }

        .highlight-error {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2));
            padding: 2px 6px;
            border-radius: 6px;
            border-bottom: 2px solid var(--danger);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .highlight-error:hover {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.3), rgba(192, 57, 43, 0.3));
            transform: scale(1.02);
        }

        @media (max-width: 1200px) {
            .score-dashboard { grid-template-columns: 1fr; }
            .analysis-section { grid-template-columns: 1fr; }
            .criteria-grid { grid-template-columns: 1fr; }
        }

        @media (max-width: 768px) {
            .header-title { font-size: 2.5rem; }
            .score-number { font-size: 4rem; }
            .content-section { padding: 30px 20px; }
            .stats-overview { grid-template-columns: repeat(2, 1fr); }
        }
    </style>
</head>
<body>
    <!-- Demo Watermark -->
    <div class="demo-watermark animate__animated animate__pulse animate__infinite">
        <i class="fas fa-flask"></i> DEMO MODE
    </div>

    <!-- Floating Actions -->
    <div class="floating-actions">
        <button class="fab" onclick="scrollToTop()" title="Scroll to top">
            <i class="fas fa-arrow-up"></i>
        </button>
        <button class="fab" onclick="toggleFullscreen()" title="Toggle fullscreen">
            <i class="fas fa-expand"></i>
        </button>
        <button class="fab" onclick="exportReport()" title="Export report">
            <i class="fas fa-download"></i>
        </button>
    </div>

    <div class="container-fluid">
        <div class="main-container animate__animated animate__fadeIn">
            <!-- Header Section -->
            <div class="header-section">
                <div class="header-content">
                    <h1 class="header-title animate__animated animate__fadeInDown">
                        <i class="fas fa-graduation-cap"></i>
                        IELTS Writing Scorer
                    </h1>
                    <p class="header-subtitle animate__animated animate__fadeInUp animate__delay-1s">
                        Advanced AI-Powered Assessment with Professional Feedback & Detailed Analysis
                    </p>
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <!-- Score Dashboard -->
                <div class="score-dashboard">
                    <div class="overall-score-card animate__animated animate__zoomIn">
                        <div class="score-number" id="overallScore">5.5</div>
                        <div class="score-label">Overall Band Score</div>
                        <div class="score-description">
                            Competent User - Good command of the language despite some inaccuracies
                        </div>
                    </div>
                    
                    <div class="criteria-breakdown animate__animated animate__fadeInRight">
                        <h3 class="criteria-title">
                            <i class="fas fa-chart-bar"></i>
                            Detailed Breakdown
                        </h3>
                        <div class="criteria-grid">
                            <div class="criteria-item">
                                <div class="criteria-name">Task Achievement</div>
                                <div class="criteria-score">5.5</div>
                                <div class="criteria-feedback">Addresses task with some clarity</div>
                            </div>
                            <div class="criteria-item">
                                <div class="criteria-name">Coherence & Cohesion</div>
                                <div class="criteria-score">5.0</div>
                                <div class="criteria-feedback">Basic organization with limited linking</div>
                            </div>
                            <div class="criteria-item">
                                <div class="criteria-name">Lexical Resource</div>
                                <div class="criteria-score">5.5</div>
                                <div class="criteria-feedback">Adequate vocabulary with some errors</div>
                            </div>
                            <div class="criteria-item">
                                <div class="criteria-name">Grammar Range & Accuracy</div>
                                <div class="criteria-score">5.0</div>
                                <div class="criteria-feedback">Limited range with frequent errors</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analysis Section -->
                <div class="analysis-section">
                    <div class="essay-analysis animate__animated animate__fadeInLeft">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-file-alt"></i>
                                Essay Analysis
                            </h3>
                            <div class="view-toggle">
                                <button class="toggle-btn active" onclick="toggleView('highlighted')">
                                    <i class="fas fa-highlighter"></i> Highlighted
                                </button>
                                <button class="toggle-btn" onclick="toggleView('clean')">
                                    <i class="fas fa-file-text"></i> Clean
                                </button>
                            </div>
                        </div>
                        <div class="essay-content" id="essayContent">
                            <!-- Essay content will be populated here -->
                        </div>
                    </div>
                    
                    <div class="corrections-panel animate__animated animate__fadeInRight">
                        <h3 class="section-title">
                            <i class="fas fa-exclamation-triangle"></i>
                            Key Corrections
                            <span class="badge bg-danger ms-2">5</span>
                        </h3>
                        <div id="correctionsContainer">
                            <!-- Corrections will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Statistics Overview -->
                <div class="stats-overview">
                    <div class="stat-card animate__animated animate__fadeInUp">
                        <div class="stat-icon">
                            <i class="fas fa-file-word"></i>
                        </div>
                        <div class="stat-number">202</div>
                        <div class="stat-label">Word Count</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-1s">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="stat-number">5</div>
                        <div class="stat-label">Corrections</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-2s">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Clean Rate</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-3s">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">12.9s</div>
                        <div class="stat-label">Process Time</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-4s">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-number">B2</div>
                        <div class="stat-label">CEFR Level</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-5s">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-number">68%</div>
                        <div class="stat-label">Accuracy</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Demo data
        const demoData = {
            "overall_band_score": 5.5,
            "highlighted_corrections": [
                {
                    "original_text": "Many peoples thinking that student must learn only brainy subjects",
                    "suggested_correction": "Many people think that students must learn only academic subjects",
                    "error_type": "grammar|vocabulary",
                    "explanation": "Incorrect plural form and verb tense; 'brainy' is informal.",
                    "severity": "high"
                },
                {
                    "original_text": "I am disagree totality.",
                    "suggested_correction": "I totally disagree.",
                    "error_type": "grammar",
                    "explanation": "Incorrect verb form and word order.",
                    "severity": "high"
                },
                {
                    "original_text": "school is not only for put information into children brain",
                    "suggested_correction": "school is not only for putting information into children's brains",
                    "error_type": "grammar",
                    "explanation": "Incorrect verb form and possessive form.",
                    "severity": "high"
                },
                {
                    "original_text": "When going to job finding, people should wearing nice clothes",
                    "suggested_correction": "When looking for a job, people should wear nice clothes",
                    "error_type": "grammar",
                    "explanation": "Incorrect verb form and phrasing.",
                    "severity": "high"
                },
                {
                    "original_text": "If student cannot cook, they will hungry",
                    "suggested_correction": "If students cannot cook, they will be hungry",
                    "error_type": "grammar",
                    "explanation": "Subject-verb agreement and missing verb.",
                    "severity": "high"
                }
            ]
        };

        const sampleEssay = `Nowadays, education is the golden key for students to open their success door. Many peoples thinking that student must learn only brainy subjects like math and chemical to becoming big guy in society. Other skill like making food or wearing cloth is useless and spend time. I am disagree totality.

In my opinion, school is not only for put information into children brain, but also for create them into good person and survive in jungle life. If student cannot cook, they will hungry and go to restaurant always, which is costly and non-healthy.

Moreover, dressing is not small matter. When going to job finding, people should wearing nice clothes to make love impression on manager. If student look messy, they maybe not get chance to job even they have big brain.

On the contradiction, I understand why people think book knowledge is more taller. Academic knowledge help in passing exam and become intelligence. But life is not only exam, it's full of many obstacle like living alone or wearing properly.

In finally, I strong believe that mixing academic and daily life skill is the best idea. Children should learn both book thing and survival tactics to becoming success in their journey.`;

        let currentView = 'highlighted';
        let activeCorrection = -1;

        function initializeDemo() {
            populateEssay();
            populateCorrections();
            animateScore();
        }

        function populateEssay() {
            const container = document.getElementById('essayContent');
            if (currentView === 'highlighted') {
                container.innerHTML = formatEssayWithHighlights(sampleEssay, demoData.highlighted_corrections);
            } else {
                container.innerHTML = sampleEssay.replace(/\n/g, '<br><br>');
            }
        }

        function formatEssayWithHighlights(essay, corrections) {
            let formattedEssay = essay;
            
            corrections.forEach((correction, index) => {
                const originalText = correction.original_text;
                const replacement = `<span class="highlight-error" onclick="selectCorrection(${index})" data-bs-toggle="tooltip" title="${correction.explanation}">${originalText}</span>`;
                formattedEssay = formattedEssay.replace(originalText, replacement);
            });
            
            return formattedEssay.replace(/\n/g, '<br><br>');
        }

        function populateCorrections() {
            const container = document.getElementById('correctionsContainer');
            
            container.innerHTML = demoData.highlighted_corrections.map((correction, index) => `
                <div class="correction-card ${index === activeCorrection ? 'active' : ''}" onclick="selectCorrection(${index})">
                    <div class="correction-header">
                        <div class="correction-number">${index + 1}</div>
                        <span class="severity-badge severity-${correction.severity}">
                            <i class="fas fa-${getSeverityIcon(correction.severity)}"></i>
                            ${correction.severity}
                        </span>
                    </div>
                    <div class="text-comparison">
                        <div class="original-text">
                            <strong><i class="fas fa-times"></i> Original:</strong><br>
                            "${correction.original_text}"
                        </div>
                        <div class="suggested-text">
                            <strong><i class="fas fa-check"></i> Suggested:</strong><br>
                            "${correction.suggested_correction}"
                        </div>
                    </div>
                    <div class="correction-explanation">
                        <i class="fas fa-lightbulb"></i> ${correction.explanation}
                    </div>
                </div>
            `).join('');
        }

        function selectCorrection(index) {
            activeCorrection = index;
            
            // Update corrections display
            populateCorrections();
            
            // Highlight in essay
            const highlights = document.querySelectorAll('.highlight-error');
            highlights.forEach((el, i) => {
                el.style.background = i === index ? 
                    'linear-gradient(135deg, rgba(231, 76, 60, 0.4), rgba(192, 57, 43, 0.4))' :
                    'linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2))';
            });
            
            if (highlights[index]) {
                highlights[index].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        function toggleView(view) {
            currentView = view;
            
            // Update toggle buttons
            document.querySelectorAll('.toggle-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update essay display
            populateEssay();
            
            // Reinitialize tooltips if highlighted view
            if (view === 'highlighted') {
                setTimeout(() => {
                    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    tooltipTriggerList.map(function (tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                }, 100);
            }
        }

        function getSeverityIcon(severity) {
            const icons = {
                'high': 'exclamation-triangle',
                'medium': 'exclamation-circle',
                'low': 'info-circle'
            };
            return icons[severity] || 'info-circle';
        }

        function animateScore() {
            const scoreElement = document.getElementById('overallScore');
            let current = 0;
            const target = 5.5;
            const increment = target / 100;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                scoreElement.textContent = current.toFixed(1);
            }, 20);
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        function exportReport() {
            // Simulate export functionality
            const toast = document.createElement('div');
            toast.className = 'toast-container position-fixed top-0 end-0 p-3';
            toast.innerHTML = `
                <div class="toast show" role="alert">
                    <div class="toast-header">
                        <i class="fas fa-download text-primary me-2"></i>
                        <strong class="me-auto">Export</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        Report exported successfully! (Demo mode)
                    </div>
                </div>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // Initialize demo when page loads
        document.addEventListener('DOMContentLoaded', initializeDemo);
    </script>
</body>
</html>
