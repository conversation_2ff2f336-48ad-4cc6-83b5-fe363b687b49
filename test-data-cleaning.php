<?php
require_once 'config.php';
require_once 'IELTSScorer.php';

// Test data cleaning functionality
echo "<h1>Data Cleaning Test</h1>";

// Create a mock response with HTML artifacts (like what AI is returning)
$mockDirtyResponse = [
    'overall_band_score' => 6.5,
    'highlighted_corrections' => [
        [
            'original_text' => 'data-correction-id="correction-0" data-original="people believes" data-suggestion="people believe" data-explanation="Subject-verb agreement" data-type="grammar" data-severity="high" title="Click for suggestion">people believes',
            'suggested_correction' => 'people believe',
            'error_type' => 'grammar',
            'explanation' => 'Subject-verb agreement error',
            'severity' => 'high'
        ],
        [
            'original_text' => '<span class="highlight vocabulary medium">lifes</span>',
            'suggested_correction' => 'lives',
            'error_type' => 'vocabulary',
            'explanation' => 'Spelling error - plural of life is lives',
            'severity' => 'medium'
        ]
    ]
];

echo "<h3>Original Dirty Data:</h3>";
echo "<pre>" . htmlspecialchars(json_encode($mockDirtyResponse, JSON_PRETTY_PRINT)) . "</pre>";

// Test the cleaning function
$scorer = new IELTSScorer();

// Use reflection to access private method
$reflection = new ReflectionClass($scorer);
$cleanMethod = $reflection->getMethod('cleanResponseData');
$cleanMethod->setAccessible(true);

$cleanedData = $cleanMethod->invoke($scorer, $mockDirtyResponse);

echo "<h3>Cleaned Data:</h3>";
echo "<pre>" . htmlspecialchars(json_encode($cleanedData, JSON_PRETTY_PRINT)) . "</pre>";

// Test individual text cleaning
$cleanTextMethod = $reflection->getMethod('cleanTextContent');
$cleanTextMethod->setAccessible(true);

$dirtyTexts = [
    'data-correction-id="correction-0" data-original="people believes" title="Click">people believes',
    '<span class="highlight">some text</span>',
    'Normal text without issues',
    'Text with &quot;quotes&quot; and &amp; symbols',
    'data-type="grammar" data-severity="high">education is important'
];

echo "<h3>Individual Text Cleaning Tests:</h3>";
foreach ($dirtyTexts as $i => $dirtyText) {
    $cleanText = $cleanTextMethod->invoke($scorer, $dirtyText);
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd;'>";
    echo "<strong>Test " . ($i + 1) . ":</strong><br>";
    echo "<strong>Dirty:</strong> " . htmlspecialchars($dirtyText) . "<br>";
    echo "<strong>Clean:</strong> " . htmlspecialchars($cleanText) . "<br>";
    echo "</div>";
}

// Test with real API call
echo "<h3>Real API Test:</h3>";
$testEssay = "Many people believes that technology have changed our lifes dramatically.";
$taskType = 'task2';
$prompt = 'Technology has changed our lives. Discuss the effects.';

try {
    echo "<p>Testing with real API call...</p>";
    $result = $scorer->scoreEssay($testEssay, $taskType, $prompt);
    
    if (isset($result['highlighted_corrections'])) {
        echo "<h4>Highlighted Corrections from API:</h4>";
        foreach ($result['highlighted_corrections'] as $i => $correction) {
            echo "<div style='margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;'>";
            echo "<strong>Correction " . ($i + 1) . ":</strong><br>";
            echo "<strong>Original:</strong> " . htmlspecialchars($correction['original_text'] ?? 'N/A') . "<br>";
            echo "<strong>Suggestion:</strong> " . htmlspecialchars($correction['suggested_correction'] ?? 'N/A') . "<br>";
            echo "<strong>Type:</strong> " . htmlspecialchars($correction['error_type'] ?? 'N/A') . "<br>";
            echo "<strong>Explanation:</strong> " . htmlspecialchars($correction['explanation'] ?? 'N/A') . "<br>";
            echo "</div>";
        }
    } else {
        echo "<p>No highlighted_corrections found in API response.</p>";
        echo "<details><summary>Full API Response</summary>";
        echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>";
        echo "</details>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
h1, h3, h4 { color: #333; }
</style>";
?>
