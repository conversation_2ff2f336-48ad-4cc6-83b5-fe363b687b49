<?php
require_once 'config.php';
require_once 'IELTSScorer.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>API Cleaning Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .correction { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>";

echo "<h1>API Data Cleaning Test</h1>";

$testEssay = "Many people believes that technology have changed our lifes dramatically. In my opinion, I think that this changes has both positive and negative effects on society.";
$taskType = 'task2';
$prompt = 'Technology has changed our lives. Discuss the positive and negative effects.';

try {
    echo "<div class='success'>Testing with essay: " . htmlspecialchars($testEssay) . "</div>";
    
    $scorer = new IELTSScorer();
    $result = $scorer->scoreEssay($testEssay, $taskType, $prompt);
    
    if (isset($result['error']) && $result['error']) {
        echo "<div class='error'>Error: " . htmlspecialchars($result['message']) . "</div>";
        if (isset($result['raw_response'])) {
            echo "<h3>Raw Response:</h3>";
            echo "<pre>" . htmlspecialchars($result['raw_response']) . "</pre>";
        }
    } else {
        echo "<div class='success'>Scoring successful! Overall score: " . ($result['overall_band_score'] ?? 'N/A') . "</div>";
        
        if (isset($result['highlighted_corrections']) && is_array($result['highlighted_corrections'])) {
            echo "<h3>Highlighted Corrections (" . count($result['highlighted_corrections']) . " found):</h3>";
            
            foreach ($result['highlighted_corrections'] as $i => $correction) {
                echo "<div class='correction'>";
                echo "<h4>Correction " . ($i + 1) . "</h4>";
                echo "<strong>Original Text:</strong> \"" . htmlspecialchars($correction['original_text'] ?? 'N/A') . "\"<br>";
                echo "<strong>Suggested:</strong> \"" . htmlspecialchars($correction['suggested_correction'] ?? 'N/A') . "\"<br>";
                echo "<strong>Type:</strong> " . htmlspecialchars($correction['error_type'] ?? 'N/A') . "<br>";
                echo "<strong>Severity:</strong> " . htmlspecialchars($correction['severity'] ?? 'N/A') . "<br>";
                echo "<strong>Explanation:</strong> " . htmlspecialchars($correction['explanation'] ?? 'N/A') . "<br>";
                
                // Check for HTML artifacts
                $hasHtml = false;
                foreach (['original_text', 'suggested_correction', 'explanation'] as $field) {
                    if (isset($correction[$field]) && (strpos($correction[$field], '<') !== false || strpos($correction[$field], 'data-') !== false)) {
                        $hasHtml = true;
                        break;
                    }
                }
                
                if ($hasHtml) {
                    echo "<div style='color: red; font-weight: bold;'>⚠️ HTML artifacts detected!</div>";
                } else {
                    echo "<div style='color: green; font-weight: bold;'>✅ Clean text</div>";
                }
                echo "</div>";
            }
            
            // Test highlighting with this data
            echo "<h3>Test Highlighting:</h3>";
            echo "<div id='testHighlighting'></div>";
            echo "<button onclick='testHighlighting()' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>Test Highlighting</button>";
            
        } else {
            echo "<div class='error'>No highlighted_corrections found in response</div>";
        }
        
        echo "<details style='margin-top: 20px;'>";
        echo "<summary>Full API Response</summary>";
        echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>";
        echo "</details>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "
<script src='assets/essay-highlighter.js'></script>
<script>
function testHighlighting() {
    const result = " . json_encode($result ?? []) . ";
    const essay = " . json_encode($testEssay) . ";
    
    console.log('Testing highlighting with:', result);
    
    if (result && result.highlighted_corrections && essay) {
        const highlighter = new EssayHighlighter('testHighlighting');
        highlighter.init(result, essay);
    } else {
        document.getElementById('testHighlighting').innerHTML = '<div class=\"error\">No valid data for highlighting test</div>';
    }
}
</script>
</body>
</html>";
?>
