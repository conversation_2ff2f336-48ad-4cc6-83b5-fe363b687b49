<?php
require_once 'config.php';
require_once 'IELTSScorer.php';

// Sample essay with intentional errors for testing highlighting
$testEssay = "Many people believes that technology have changed our lifes dramatically. In my opinion, I think that this changes has both positive and negative effects on society.

Firstly, technology has made communication more easier than before. People can now contact with their friends and family members who live in different countries through social media platforms like Facebook and WhatsApp. This is very convenient and save time.

Secondly, technology has improved education system. Students can access to online courses and learn from the best teachers around the world. They don't need to travel to attend classes, which is cost-effective.

However, there are also some disadvantages. Many young people are becoming addicted to their smartphones and spend too much time on social media instead of studying or socializing with real people. This can effect their mental health and social skills.

In conclusion, although technology brings many benefits, we should use it wisely to avoid the negative consequences.";

$taskType = 'task2';
$prompt = 'Technology has changed our lives dramatically. Discuss the positive and negative effects of technology on society.';

echo "<!DOCTYPE html>
<html>
<head>
    <title>IELTS Highlighting Test</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <link href='assets/essay-highlighter.css' rel='stylesheet'>
    <style>
        body { padding: 20px; }
        .test-container { max-width: 1200px; margin: 0 auto; }
        .original-essay { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class='test-container'>
        <h1><i class='fas fa-test-tube'></i> IELTS Highlighting Feature Test</h1>
        
        <div class='original-essay'>
            <h3>Original Essay (with intentional errors):</h3>
            <p>" . nl2br(htmlspecialchars($testEssay)) . "</p>
        </div>";

echo "<h3>Scoring and Highlighting...</h3>";
echo "<div id='loadingSpinner' class='text-center'>
        <div class='spinner-border text-primary' role='status'>
            <span class='visually-hidden'>Loading...</span>
        </div>
        <p>Analyzing essay and generating corrections...</p>
      </div>";

try {
    $scorer = new IELTSScorer();
    $result = $scorer->scoreEssay($testEssay, $taskType, $prompt);
    
    if (isset($result['error']) && $result['error']) {
        echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($result['message']) . "</div>";
        if (isset($result['raw_response'])) {
            echo "<h4>Raw Response:</h4>";
            echo "<pre>" . htmlspecialchars($result['raw_response']) . "</pre>";
        }
    } else {
        echo "<div class='alert alert-success'>
                <h4>Scoring Complete!</h4>
                <p>Overall Band Score: <strong>" . ($result['overall_band_score'] ?? 'N/A') . "</strong></p>
              </div>";
        
        // Display highlighted essay container
        echo "<div id='highlightedEssayContainer'></div>";
        
        // Display raw result for debugging
        echo "<details class='mt-4'>
                <summary>Raw Scoring Result (for debugging)</summary>
                <pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>
              </details>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Exception: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div>

<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
<script src='assets/essay-highlighter.js'></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Hide loading spinner
    document.getElementById('loadingSpinner').style.display = 'none';
    
    // Initialize highlighter if we have results
    const scoringResult = " . json_encode($result ?? null) . ";
    const originalEssay = " . json_encode($testEssay) . ";
    
    if (scoringResult && !scoringResult.error && originalEssay) {
        console.log('Initializing highlighter with result:', scoringResult);
        
        const highlighter = new EssayHighlighter('highlightedEssayContainer');
        highlighter.init(scoringResult, originalEssay);
    } else {
        console.log('No valid scoring result for highlighting');
        document.getElementById('highlightedEssayContainer').innerHTML = 
            '<div class=\"alert alert-warning\">No highlighting data available. The AI may not have provided the expected format.</div>';
    }
});
</script>
</body>
</html>";
?>
