<?php
require_once 'config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>API Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>";

echo "<h1>🔧 API Connection Diagnostic Test</h1>";

// Test 1: Check configuration
echo "<h3>1. Configuration Check</h3>";
echo "<div class='info'>";
echo "<strong>API URL:</strong> " . OPENAI_API_URL . "<br>";
echo "<strong>API Key:</strong> " . substr(OPENAI_API_KEY, 0, 10) . "..." . substr(OPENAI_API_KEY, -5) . "<br>";
echo "<strong>Model:</strong> " . OPENAI_MODEL . "<br>";
echo "</div>";

// Test 2: Simple API call
echo "<h3>2. Simple API Connection Test</h3>";

$testData = [
    'model' => OPENAI_MODEL,
    'messages' => [
        [
            'role' => 'user',
            'content' => 'Say "Hello, API is working!" and nothing else.'
        ]
    ],
    'max_tokens' => 50,
    'temperature' => 0.1
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, OPENAI_API_URL);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . OPENAI_API_KEY
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

if ($curlError) {
    echo "<div class='error'>";
    echo "<strong>❌ CURL Error:</strong> " . htmlspecialchars($curlError);
    echo "</div>";
} else {
    echo "<div class='info'>";
    echo "<strong>HTTP Status Code:</strong> " . $httpCode . "<br>";
    echo "</div>";
    
    if ($httpCode === 200) {
        echo "<div class='success'>";
        echo "<strong>✅ API Connection Successful!</strong>";
        echo "</div>";
        
        $decoded = json_decode($response, true);
        if ($decoded && isset($decoded['choices'][0]['message']['content'])) {
            echo "<div class='success'>";
            echo "<strong>API Response:</strong> " . htmlspecialchars($decoded['choices'][0]['message']['content']);
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<strong>❌ Invalid Response Format</strong>";
            echo "</div>";
        }
    } else {
        echo "<div class='error'>";
        echo "<strong>❌ API Error (HTTP {$httpCode})</strong>";
        echo "</div>";
    }
    
    echo "<details>";
    echo "<summary>Raw Response</summary>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    echo "</details>";
}

// Test 3: Test IELTSScorer class
echo "<h3>3. IELTSScorer Class Test</h3>";

try {
    require_once 'IELTSScorer.php';
    $scorer = new IELTSScorer();
    
    echo "<div class='success'>";
    echo "✅ IELTSScorer class loaded successfully";
    echo "</div>";
    
    // Test with a very simple essay
    $testEssay = "Education is important for everyone.";
    $result = $scorer->scoreEssay($testEssay, 'task2', 'Discuss the importance of education.');
    
    if (isset($result['error']) && $result['error']) {
        echo "<div class='error'>";
        echo "<strong>❌ Scoring Error:</strong> " . htmlspecialchars($result['message'] ?? 'Unknown error');
        echo "</div>";
        
        if (isset($result['raw_response'])) {
            echo "<details>";
            echo "<summary>Raw Response</summary>";
            echo "<pre>" . htmlspecialchars($result['raw_response']) . "</pre>";
            echo "</details>";
        }
    } else {
        echo "<div class='success'>";
        echo "<strong>✅ Scoring Successful!</strong><br>";
        echo "Band Score: " . ($result['overall_band_score'] ?? 'N/A') . "<br>";
        echo "Corrections Found: " . (isset($result['highlighted_corrections']) ? count($result['highlighted_corrections']) : 0);
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<strong>❌ Exception:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

// Test 4: Check file permissions and includes
echo "<h3>4. File System Check</h3>";

$files = ['config.php', 'IELTSScorer.php', 'assets/essay-highlighter.js', 'assets/essay-highlighter.css'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ {$file} exists and readable</div>";
    } else {
        echo "<div class='error'>❌ {$file} not found</div>";
    }
}

echo "</body></html>";
?>
