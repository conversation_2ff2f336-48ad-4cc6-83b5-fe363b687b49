<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Highlighting Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/essay-highlighter.css" rel="stylesheet">
    <style>
        body { padding: 20px; background: #f8f9fa; }
        .test-container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-test-tube"></i> Simple Highlighting Test</h1>
        <p class="text-muted">Testing highlighting functionality with mock data</p>
        
        <div id="highlightedEssayContainer"></div>
        
        <div class="mt-4">
            <button class="btn btn-primary" onclick="testHighlighting()">Test Highlighting</button>
            <button class="btn btn-secondary" onclick="clearTest()">Clear</button>
        </div>
        
        <div class="mt-4">
            <h5>Console Output:</h5>
            <div id="consoleOutput" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap;"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/essay-highlighter.js"></script>
    <script>
        // Override console.log to display in page
        const originalLog = console.log;
        const consoleOutput = document.getElementById('consoleOutput');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            consoleOutput.textContent += args.join(' ') + '\n';
        };

        function testHighlighting() {
            console.log('=== Starting Highlighting Test ===');
            
            // Mock essay with intentional errors
            const originalEssay = `Many people believes that technology have changed our lifes dramatically. In my opinion, I think that this changes has both positive and negative effects on society.

Firstly, technology has made communication more easier than before. People can now contact with their friends and family members who live in different countries through social media platforms like Facebook and WhatsApp. This is very convenient and save time.

However, there are also some disadvantages. Many young people are becoming addicted to their smartphones and spend too much time on social media instead of studying or socializing with real people. This can effect their mental health and social skills.

In conclusion, although technology brings many benefits, we should use it wisely to avoid the negative consequences.`;

            // Mock scoring result with clean text
            const mockScoringResult = {
                overall_band_score: 6.5,
                highlighted_corrections: [
                    {
                        original_text: "Many people believes",
                        suggested_correction: "Many people believe",
                        error_type: "grammar",
                        explanation: "Subject-verb agreement error. 'People' is plural, so use 'believe'.",
                        severity: "high"
                    },
                    {
                        original_text: "technology have changed",
                        suggested_correction: "technology has changed",
                        error_type: "grammar",
                        explanation: "Subject-verb agreement. 'Technology' is singular, so use 'has'.",
                        severity: "high"
                    },
                    {
                        original_text: "our lifes",
                        suggested_correction: "our lives",
                        error_type: "vocabulary",
                        explanation: "Spelling error. The plural of 'life' is 'lives'.",
                        severity: "medium"
                    },
                    {
                        original_text: "this changes has",
                        suggested_correction: "these changes have",
                        error_type: "grammar",
                        explanation: "Use 'these' for plural and 'have' for plural subject.",
                        severity: "high"
                    },
                    {
                        original_text: "more easier",
                        suggested_correction: "easier",
                        error_type: "vocabulary",
                        explanation: "Double comparative error. Use either 'easier' or 'more easy' (though 'easier' is preferred).",
                        severity: "medium"
                    },
                    {
                        original_text: "contact with",
                        suggested_correction: "contact",
                        error_type: "vocabulary",
                        explanation: "The verb 'contact' doesn't need the preposition 'with' in this context.",
                        severity: "low"
                    },
                    {
                        original_text: "This can effect",
                        suggested_correction: "This can affect",
                        error_type: "vocabulary",
                        explanation: "'Affect' is the verb, 'effect' is the noun.",
                        severity: "high"
                    }
                ]
            };

            console.log('Mock data prepared');
            console.log('Original essay length:', originalEssay.length);
            console.log('Number of corrections:', mockScoringResult.highlighted_corrections.length);

            // Initialize highlighter
            const highlighter = new EssayHighlighter('highlightedEssayContainer');
            highlighter.init(mockScoringResult, originalEssay);
            
            console.log('=== Highlighting Test Complete ===');
        }

        function clearTest() {
            document.getElementById('highlightedEssayContainer').innerHTML = '';
            document.getElementById('consoleOutput').textContent = '';
            console.log('Test cleared');
        }

        // Auto-run test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, ready for testing');
        });
    </script>
</body>
</html>
