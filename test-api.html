<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - IELTS Scorer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; padding: 20px; }
        .container { max-width: 1200px; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #dee2e6; border-radius: 10px; }
        .json-display { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
        .status-indicator { width: 12px; height: 12px; border-radius: 50%; display: inline-block; margin-right: 8px; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-loading { background: #ffc107; animation: pulse 1s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-flask"></i> API Test - IELTS Scorer</h1>
        <p class="lead">Test the new client-side API integration</p>

        <!-- API Test Form -->
        <div class="test-section">
            <h3><i class="fas fa-play"></i> API Test</h3>
            <form id="apiTestForm">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="testTaskType" class="form-label">Task Type</label>
                        <select class="form-select" id="testTaskType" required>
                            <option value="task2">Task 2 (Essay)</option>
                            <option value="task1_academic">Task 1 Academic</option>
                            <option value="task1_general">Task 1 General</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="testPrompt" class="form-label">Prompt</label>
                        <input type="text" class="form-control" id="testPrompt" 
                               value="Discuss the importance of education in modern society.">
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="testEssay" class="form-label">Test Essay</label>
                    <textarea class="form-control" id="testEssay" rows="8" required>Many people believes that education is very important for success in modern society. Students should study hard to achieve their goals and get good jobs in the future. Education helps people to develop their skills and knowledge, which are essential for personal and professional growth.</textarea>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-rocket"></i> Test API
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearResults()">
                    <i class="fas fa-trash"></i> Clear Results
                </button>
            </form>
        </div>

        <!-- Status Section -->
        <div class="test-section">
            <h3><i class="fas fa-info-circle"></i> Status</h3>
            <div id="statusDisplay">
                <span class="status-indicator status-success"></span>
                Ready to test
            </div>
        </div>

        <!-- Request Section -->
        <div class="test-section">
            <h3><i class="fas fa-arrow-up"></i> Request Data</h3>
            <div id="requestDisplay" class="json-display">
                No request sent yet
            </div>
        </div>

        <!-- Response Section -->
        <div class="test-section">
            <h3><i class="fas fa-arrow-down"></i> API Response</h3>
            <div id="responseDisplay" class="json-display">
                No response received yet
            </div>
        </div>

        <!-- Processed Results Section -->
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> Processed Results</h3>
            <div id="processedDisplay">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Overall Score</h5>
                        <div class="h2 text-primary" id="testOverallScore">-</div>
                    </div>
                    <div class="col-md-6">
                        <h5>Corrections Found</h5>
                        <div class="h2 text-info" id="testCorrectionsCount">-</div>
                    </div>
                </div>
                
                <h5 class="mt-3">Sample Corrections</h5>
                <div id="testCorrections">
                    No corrections to display
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="test-section">
            <h3><i class="fas fa-tachometer-alt"></i> Performance Metrics</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">Request Time</h5>
                            <div class="h4 text-primary" id="requestTime">-</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">Response Size</h5>
                            <div class="h4 text-info" id="responseSize">-</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">Clean Corrections</h5>
                            <div class="h4 text-success" id="cleanCorrections">-</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">Status</h5>
                            <div class="h4" id="apiStatus">Ready</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testStartTime = 0;

        document.getElementById('apiTestForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await testAPI();
        });

        async function testAPI() {
            const statusDisplay = document.getElementById('statusDisplay');
            const requestDisplay = document.getElementById('requestDisplay');
            const responseDisplay = document.getElementById('responseDisplay');

            try {
                // Update status
                statusDisplay.innerHTML = '<span class="status-indicator status-loading"></span>Testing API...';
                
                // Prepare request data
                const requestData = {
                    essay: document.getElementById('testEssay').value,
                    task_type: document.getElementById('testTaskType').value,
                    prompt: document.getElementById('testPrompt').value
                };

                // Display request
                requestDisplay.textContent = JSON.stringify(requestData, null, 2);

                // Record start time
                testStartTime = performance.now();

                // Make API call
                const response = await fetch('api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const endTime = performance.now();
                const duration = ((endTime - testStartTime) / 1000).toFixed(2);

                // Get response data
                const responseData = await response.json();

                // Display response
                responseDisplay.textContent = JSON.stringify(responseData, null, 2);

                // Update status
                if (response.ok && !responseData.error) {
                    statusDisplay.innerHTML = '<span class="status-indicator status-success"></span>API test successful!';
                    processResults(responseData, duration, JSON.stringify(responseData).length);
                } else {
                    statusDisplay.innerHTML = '<span class="status-indicator status-error"></span>API test failed: ' + (responseData.message || 'Unknown error');
                    updateMetrics(duration, JSON.stringify(responseData).length, 'Error');
                }

            } catch (error) {
                console.error('API test error:', error);
                statusDisplay.innerHTML = '<span class="status-indicator status-error"></span>API test failed: ' + error.message;
                responseDisplay.textContent = 'Error: ' + error.message;
                updateMetrics(0, 0, 'Failed');
            }
        }

        function processResults(data, duration, responseSize) {
            // Update overall score
            document.getElementById('testOverallScore').textContent = data.overall_band_score || 'N/A';

            // Update corrections
            const corrections = data.highlighted_corrections || [];
            document.getElementById('testCorrectionsCount').textContent = corrections.length;

            // Display sample corrections
            const correctionsContainer = document.getElementById('testCorrections');
            if (corrections.length > 0) {
                const sampleCorrections = corrections.slice(0, 3).map((correction, index) => `
                    <div class="card mb-2">
                        <div class="card-body">
                            <h6>Correction ${index + 1}</h6>
                            <p><strong>Original:</strong> "${correction.original_text || 'N/A'}"</p>
                            <p><strong>Suggested:</strong> "${correction.suggested_correction || 'N/A'}"</p>
                            <p><strong>Type:</strong> ${correction.error_type || 'N/A'}</p>
                        </div>
                    </div>
                `).join('');
                correctionsContainer.innerHTML = sampleCorrections;
            } else {
                correctionsContainer.innerHTML = '<p class="text-muted">No corrections found</p>';
            }

            // Count clean corrections
            const cleanCount = corrections.filter(c => 
                c.original_text && 
                c.original_text.indexOf('data-') === -1 && 
                c.original_text.indexOf('<') === -1
            ).length;

            updateMetrics(duration, responseSize, 'Success', cleanCount);
        }

        function updateMetrics(duration, responseSize, status, cleanCount = 0) {
            document.getElementById('requestTime').textContent = duration + 's';
            document.getElementById('responseSize').textContent = (responseSize / 1024).toFixed(1) + 'KB';
            document.getElementById('cleanCorrections').textContent = cleanCount;
            
            const statusElement = document.getElementById('apiStatus');
            statusElement.textContent = status;
            statusElement.className = 'h4 text-' + (status === 'Success' ? 'success' : status === 'Error' ? 'danger' : 'warning');
        }

        function clearResults() {
            document.getElementById('requestDisplay').textContent = 'No request sent yet';
            document.getElementById('responseDisplay').textContent = 'No response received yet';
            document.getElementById('statusDisplay').innerHTML = '<span class="status-indicator status-success"></span>Ready to test';
            document.getElementById('testOverallScore').textContent = '-';
            document.getElementById('testCorrectionsCount').textContent = '-';
            document.getElementById('testCorrections').textContent = 'No corrections to display';
            updateMetrics(0, 0, 'Ready');
        }
    </script>
</body>
</html>
