<!DOCTYPE html>
<html>
<head>
    <title>Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body><h1>≡ƒº¬ Simple System Test</h1><h3>1. File Check</h3><div class='success'>Γ£à config.php exists</div><div class='success'>Γ£à IELTSScorer.php exists</div><h3>2. Include Test</h3><div class='success'>Γ£à config.php included</div><div class='success'>Γ£à IELTSScorer.php included</div><h3>3. Configuration Check</h3><div class='success'>Γ£à OPENAI_API_URL defined</div><div class='success'>Γ£à OPENAI_API_KEY defined</div><div class='success'>Γ£à OPENAI_MODEL defined</div><h3>4. Scorer Instance Test</h3><div class='success'>Γ£à IELTSScorer instance created</div><h3>5. Simple API Test</h3><div class='success'>Γ£à API call successful</div><div class='info'>Band Score: 2</div></body></html>
