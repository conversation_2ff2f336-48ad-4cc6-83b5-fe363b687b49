<?php
// Test nuclear cleaning with exact data from screenshot

function extractPureText($text) {
    if (!$text) return '';
    
    // Step 1: If it looks like HTML, extract the actual text content
    if (strpos($text, 'data-') !== false || strpos($text, '<') !== false) {
        // Method 1: Extract text after the last '>' 
        if (preg_match('/>[^<>]*$/', $text, $matches)) {
            $extracted = trim(str_replace('>', '', $matches[0]));
            if (!empty($extracted) && strpos($extracted, 'data-') === false) {
                $text = $extracted;
            }
        }
        
        // Method 2: If still dirty, look for quoted text that makes sense
        if (strpos($text, 'data-') !== false) {
            if (preg_match('/data-original="([^"]*)"/', $text, $matches)) {
                if (!empty($matches[1]) && strlen($matches[1]) > 2) {
                    $text = $matches[1];
                }
            }
        }
        
        // Method 3: Last resort - split and find the longest meaningful part
        if (strpos($text, 'data-') !== false || strpos($text, '<') !== false) {
            $parts = preg_split('/[<>"]/', $text);
            $longest = '';
            foreach ($parts as $part) {
                $part = trim($part);
                if (strlen($part) > strlen($longest) && 
                    strpos($part, 'data-') === false && 
                    strpos($part, '=') === false &&
                    strlen($part) > 2) {
                    $longest = $part;
                }
            }
            if (!empty($longest)) {
                $text = $longest;
            }
        }
    }
    
    // Step 2: Standard cleaning
    $text = strip_tags($text);
    $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    $text = preg_replace('/\s+/', ' ', $text);
    $text = trim($text);
    
    return $text;
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>Nuclear Cleaning Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 10px; }
        .dirty { background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .clean { background: #e6ffe6; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .result { font-weight: bold; margin-top: 15px; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>";

echo "<h1>🚀 Nuclear Text Cleaning Test</h1>";
echo "<p>Testing with exact problematic data from the screenshot</p>";

// Exact data from the screenshot
$realProblematicCases = [
    'data-correction-id="correction-7" data-original="education is important than ever" data-suggestion="education is more important than ever" data-explanation="The comparative form \'more important\' is needed here." data-type="grammar" data-severity="high" title="Click for suggestion">education is important than ever',
    
    'data-correction-id="correction-3" data-original="students should only learn knowledge from books" data-suggestion="students should only learn knowledge from textbooks" data-explanation="The term \'textbooks\' is more precise than \'books\' in an educational context." data-type="vocabulary" data-severity="medium">students should only learn knowledge from books'
];

foreach ($realProblematicCases as $i => $testCase) {
    echo "<div class='test'>";
    echo "<h3>🧪 Real Case " . ($i + 1) . " from Screenshot</h3>";
    
    echo "<div class='dirty'>";
    echo "<strong>🔴 Original Problematic Data:</strong><br>";
    echo "<code>" . htmlspecialchars($testCase) . "</code>";
    echo "</div>";
    
    $cleaned = extractPureText($testCase);
    
    echo "<div class='clean'>";
    echo "<strong>✅ Nuclear Cleaned Result:</strong><br>";
    echo "<code>" . htmlspecialchars($cleaned) . "</code>";
    echo "</div>";
    
    // Validation
    $isClean = (strpos($cleaned, 'data-') === false && 
                strpos($cleaned, '<') === false && 
                strpos($cleaned, '=') === false &&
                !empty($cleaned) &&
                strlen($cleaned) > 2);
    
    if ($isClean) {
        echo "<div class='result success'>";
        echo "🎉 SUCCESS: Completely clean text extracted!<br>";
        echo "Length: " . strlen($cleaned) . " characters<br>";
        echo "Ready for highlighting: YES";
        echo "</div>";
    } else {
        echo "<div class='result fail'>";
        echo "❌ FAILED: Still contains artifacts or empty<br>";
        if (empty($cleaned)) echo "- Text is empty<br>";
        if (strpos($cleaned, 'data-') !== false) echo "- Contains data attributes<br>";
        if (strpos($cleaned, '<') !== false) echo "- Contains HTML tags<br>";
        if (strpos($cleaned, '=') !== false) echo "- Contains equals signs<br>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Test with a mock correction array like what would come from API
echo "<div class='test'>";
echo "<h3>🔬 Full Correction Object Test</h3>";

$mockCorrection = [
    'original_text' => 'data-correction-id="correction-7" data-original="education is important than ever" data-suggestion="education is more important than ever" title="Click for suggestion">education is important than ever',
    'suggested_correction' => 'education is more important than ever',
    'error_type' => 'grammar',
    'explanation' => 'The comparative form is needed',
    'severity' => 'high'
];

echo "<div class='dirty'>";
echo "<strong>🔴 Mock API Response:</strong><br>";
echo "<pre>" . htmlspecialchars(json_encode($mockCorrection, JSON_PRETTY_PRINT)) . "</pre>";
echo "</div>";

$cleanedCorrection = [
    'original_text' => extractPureText($mockCorrection['original_text']),
    'suggested_correction' => extractPureText($mockCorrection['suggested_correction']),
    'error_type' => $mockCorrection['error_type'],
    'explanation' => extractPureText($mockCorrection['explanation']),
    'severity' => $mockCorrection['severity']
];

echo "<div class='clean'>";
echo "<strong>✅ Nuclear Cleaned Correction:</strong><br>";
echo "<pre>" . htmlspecialchars(json_encode($cleanedCorrection, JSON_PRETTY_PRINT)) . "</pre>";
echo "</div>";

$allFieldsClean = true;
foreach (['original_text', 'suggested_correction', 'explanation'] as $field) {
    if (strpos($cleanedCorrection[$field], 'data-') !== false || 
        strpos($cleanedCorrection[$field], '<') !== false) {
        $allFieldsClean = false;
        break;
    }
}

if ($allFieldsClean && !empty($cleanedCorrection['original_text'])) {
    echo "<div class='result success'>";
    echo "🏆 PERFECT: All fields are completely clean and ready for highlighting!";
    echo "</div>";
} else {
    echo "<div class='result fail'>";
    echo "❌ Some fields still have issues";
    echo "</div>";
}

echo "</div>";

echo "</body></html>";
?>
