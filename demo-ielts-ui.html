<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IELTS Writing Scorer - Professional Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            color: #333;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            margin: 20px auto;
            max-width: 1400px;
            overflow: hidden;
        }

        .header-section {
            background: var(--primary-gradient);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .content-section {
            padding: 40px;
        }

        .score-overview {
            background: var(--success-gradient);
            color: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .score-overview::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }

        .score-number {
            font-size: 5rem;
            font-weight: 800;
            margin-bottom: 15px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.2);
            position: relative;
            z-index: 2;
        }

        .score-label {
            font-size: 1.5rem;
            font-weight: 500;
            opacity: 0.95;
            position: relative;
            z-index: 2;
        }

        .criteria-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .criteria-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .criteria-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--info-gradient);
        }

        .criteria-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.12);
        }

        .criteria-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .criteria-score {
            font-size: 2.5rem;
            font-weight: 700;
            background: var(--info-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .essay-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .essay-content {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            line-height: 1.8;
            font-size: 1.05rem;
            border-left: 4px solid #667eea;
        }

        .correction-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.06);
            border-left: 4px solid #f093fb;
            transition: all 0.3s ease;
        }

        .correction-item:hover {
            transform: translateX(5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .correction-highlight {
            border-left: 4px solid #667eea !important;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)) !important;
            transform: translateX(10px) !important;
        }

        .correction-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .correction-number {
            background: var(--warning-gradient);
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .severity-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .severity-high {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .severity-medium {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
            color: white;
        }

        .severity-low {
            background: linear-gradient(135deg, #48dbfb, #0abde3);
            color: white;
        }

        .correction-text {
            margin-bottom: 10px;
        }

        .original-text {
            background: #ffe6e6;
            padding: 8px 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            border-left: 3px solid #ff6b6b;
        }

        .suggested-text {
            background: #e6ffe6;
            padding: 8px 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            border-left: 3px solid #27ae60;
        }

        .correction-explanation {
            background: #f0f8ff;
            padding: 12px;
            border-radius: 8px;
            font-style: italic;
            color: #2c3e50;
            border-left: 3px solid #3498db;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 6px 20px rgba(0,0,0,0.06);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            background: var(--dark-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #7f8c8d;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .demo-badge {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--warning-gradient);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .floating-action {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(0,0,0,0.3);
        }

        @media (max-width: 768px) {
            .header-title { font-size: 2rem; }
            .score-number { font-size: 3.5rem; }
            .content-section { padding: 20px; }
            .criteria-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <!-- Demo Badge -->
    <div class="demo-badge">
        <i class="fas fa-flask"></i> DEMO MODE
    </div>

    <!-- Floating Action Button -->
    <button class="floating-action" onclick="scrollToTop()">
        <i class="fas fa-arrow-up"></i>
    </button>

    <div class="container-fluid">
        <div class="main-container">
            <!-- Header Section -->
            <div class="header-section">
                <div class="header-content">
                    <h1 class="header-title">
                        <i class="fas fa-graduation-cap"></i>
                        IELTS Writing Scorer
                    </h1>
                    <p class="header-subtitle">
                        Professional AI-Powered Assessment with Detailed Feedback
                    </p>
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <!-- Overall Score -->
                <div class="score-overview">
                    <div class="score-number" id="overallScore">5.5</div>
                    <div class="score-label">Overall Band Score</div>
                    <div class="mt-3">
                        <small style="opacity: 0.9;">Competent User - Good command of the language despite some inaccuracies</small>
                    </div>
                </div>

                <!-- Criteria Breakdown -->
                <div class="criteria-grid">
                    <div class="criteria-card">
                        <div class="criteria-title">
                            <i class="fas fa-target"></i>
                            Task Achievement
                        </div>
                        <div class="criteria-score">5.5</div>
                        <small class="text-muted">Addresses the task with some clarity</small>
                    </div>
                    <div class="criteria-card">
                        <div class="criteria-title">
                            <i class="fas fa-link"></i>
                            Coherence & Cohesion
                        </div>
                        <div class="criteria-score">5.0</div>
                        <small class="text-muted">Some organization with basic linking</small>
                    </div>
                    <div class="criteria-card">
                        <div class="criteria-title">
                            <i class="fas fa-book"></i>
                            Lexical Resource
                        </div>
                        <div class="criteria-score">5.5</div>
                        <small class="text-muted">Adequate vocabulary with some errors</small>
                    </div>
                    <div class="criteria-card">
                        <div class="criteria-title">
                            <i class="fas fa-language"></i>
                            Grammar Range & Accuracy
                        </div>
                        <div class="criteria-score">5.0</div>
                        <small class="text-muted">Limited range with frequent errors</small>
                    </div>
                </div>

                <!-- Essay Analysis -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="essay-section">
                            <h3 class="section-title">
                                <i class="fas fa-file-alt"></i>
                                Your Essay Analysis
                            </h3>
                            <div class="essay-content" id="essayContent">
                                <!-- Essay content will be populated here -->
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="essay-section">
                            <h3 class="section-title">
                                <i class="fas fa-exclamation-triangle"></i>
                                Key Corrections
                            </h3>
                            <div id="correctionsContainer">
                                <!-- Corrections will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">202</div>
                        <div class="stat-label">Word Count</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">5</div>
                        <div class="stat-label">Corrections</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Clean Rate</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">12.9s</div>
                        <div class="stat-label">Process Time</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Demo data from your JSON
        const demoData = {
            "overall_band_score": 5.5,
            "highlighted_corrections": [
                {
                    "original_text": "Many peoples thinking that student must learn only brainy subjects",
                    "suggested_correction": "Many people think that students must learn only academic subjects",
                    "error_type": "grammar|vocabulary",
                    "explanation": "Incorrect plural form and verb tense; 'brainy' is informal.",
                    "severity": "high"
                },
                {
                    "original_text": "I am disagree totality.",
                    "suggested_correction": "I totally disagree.",
                    "error_type": "grammar",
                    "explanation": "Incorrect verb form and word order.",
                    "severity": "high"
                },
                {
                    "original_text": "school is not only for put information into children brain",
                    "suggested_correction": "school is not only for putting information into children's brains",
                    "error_type": "grammar",
                    "explanation": "Incorrect verb form and possessive form.",
                    "severity": "high"
                },
                {
                    "original_text": "When going to job finding, people should wearing nice clothes",
                    "suggested_correction": "When looking for a job, people should wear nice clothes",
                    "error_type": "grammar",
                    "explanation": "Incorrect verb form and phrasing.",
                    "severity": "high"
                },
                {
                    "original_text": "If student cannot cook, they will hungry",
                    "suggested_correction": "If students cannot cook, they will be hungry",
                    "error_type": "grammar",
                    "explanation": "Subject-verb agreement and missing verb.",
                    "severity": "high"
                }
            ],
            "request_metadata": {
                "word_count": 202,
                "processing_time": 12.89883804321289
            }
        };

        // Sample essay text for demo
        const sampleEssay = `Nowadays, education is the golden key for students to open their success door. Many peoples thinking that student must learn only brainy subjects like math and chemical to becoming big guy in society. Other skill like making food or wearing cloth is useless and spend time. I am disagree totality.

In my opinion, school is not only for put information into children brain, but also for create them into good person and survive in jungle life. If student cannot cook, they will hungry and go to restaurant always, which is costly and non-healthy.

Moreover, dressing is not small matter. When going to job finding, people should wearing nice clothes to make love impression on manager. If student look messy, they maybe not get chance to job even they have big brain.

On the contradiction, I understand why people think book knowledge is more taller. Academic knowledge help in passing exam and become intelligence. But life is not only exam, it's full of many obstacle like living alone or wearing properly.

In finally, I strong believe that mixing academic and daily life skill is the best idea. Children should learn both book thing and survival tactics to becoming success in their journey.`;

        function initializeDemo() {
            // Populate essay content
            document.getElementById('essayContent').innerHTML = formatEssayWithHighlights(sampleEssay, demoData.highlighted_corrections);
            
            // Populate corrections
            populateCorrections(demoData.highlighted_corrections);
            
            // Add some animations
            animateElements();
        }

        function formatEssayWithHighlights(essay, corrections) {
            let formattedEssay = essay;
            
            corrections.forEach((correction, index) => {
                const originalText = correction.original_text;
                const highlightClass = `highlight-${correction.severity}`;
                const replacement = `<span class="${highlightClass}" data-bs-toggle="tooltip" data-bs-placement="top" title="${correction.explanation}" style="background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; padding: 2px 6px; border-radius: 4px; cursor: pointer;">${originalText}</span>`;
                formattedEssay = formattedEssay.replace(originalText, replacement);
            });
            
            return formattedEssay.replace(/\n/g, '<br><br>');
        }

        function populateCorrections(corrections) {
            const container = document.getElementById('correctionsContainer');

            container.innerHTML = corrections.map((correction, index) => `
                <div class="correction-item" onclick="highlightCorrection(${index})">
                    <div class="correction-header">
                        <div class="correction-number">${index + 1}</div>
                        <span class="severity-badge severity-${correction.severity}">
                            <i class="fas fa-${getSeverityIcon(correction.severity)}"></i>
                            ${correction.severity}
                        </span>
                    </div>
                    <div class="correction-text">
                        <div class="mb-2">
                            <strong><i class="fas fa-times-circle text-danger"></i> Original:</strong>
                            <div class="original-text mt-1">"${correction.original_text}"</div>
                        </div>
                        <div class="mb-2">
                            <strong><i class="fas fa-check-circle text-success"></i> Suggested:</strong>
                            <div class="suggested-text mt-1">"${correction.suggested_correction}"</div>
                        </div>
                        <div class="mb-2">
                            <strong><i class="fas fa-tag"></i> Type:</strong>
                            <span class="badge bg-info ms-2">${correction.error_type}</span>
                        </div>
                        <div class="correction-explanation">
                            <i class="fas fa-lightbulb"></i> ${correction.explanation}
                        </div>
                    </div>
                    <div class="mt-2 text-end">
                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); showDetailedAnalysis(${index})">
                            <i class="fas fa-search-plus"></i> Analyze
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function getSeverityIcon(severity) {
            const icons = {
                'high': 'exclamation-triangle',
                'medium': 'exclamation-circle',
                'low': 'info-circle'
            };
            return icons[severity] || 'info-circle';
        }

        function highlightCorrection(index) {
            // Remove previous highlights
            document.querySelectorAll('.correction-highlight').forEach(el => {
                el.classList.remove('correction-highlight');
            });

            // Add highlight to selected correction
            const correctionItems = document.querySelectorAll('.correction-item');
            correctionItems[index].classList.add('correction-highlight');

            // Scroll to the highlighted text in essay
            const highlightedTexts = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            if (highlightedTexts[index]) {
                highlightedTexts[index].scrollIntoView({ behavior: 'smooth', block: 'center' });
                highlightedTexts[index].style.animation = 'pulse 1s ease-in-out 3';
            }
        }

        function showDetailedAnalysis(index) {
            const correction = demoData.highlighted_corrections[index];

            // Create modal content
            const modalContent = `
                <div class="modal fade" id="analysisModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-microscope"></i> Detailed Analysis - Correction ${index + 1}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-times-circle text-danger"></i> Original Text</h6>
                                        <div class="p-3 bg-light border-start border-danger border-4 mb-3">
                                            <code>${correction.original_text}</code>
                                        </div>

                                        <h6><i class="fas fa-check-circle text-success"></i> Corrected Text</h6>
                                        <div class="p-3 bg-light border-start border-success border-4 mb-3">
                                            <code>${correction.suggested_correction}</code>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-info-circle"></i> Error Details</h6>
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex justify-content-between">
                                                <strong>Type:</strong>
                                                <span class="badge bg-info">${correction.error_type}</span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                <strong>Severity:</strong>
                                                <span class="badge bg-${correction.severity === 'high' ? 'danger' : correction.severity === 'medium' ? 'warning' : 'info'}">${correction.severity}</span>
                                            </li>
                                        </ul>

                                        <h6 class="mt-3"><i class="fas fa-lightbulb"></i> Explanation</h6>
                                        <div class="alert alert-info">
                                            ${correction.explanation}
                                        </div>

                                        <h6><i class="fas fa-book"></i> Grammar Rule</h6>
                                        <div class="alert alert-secondary">
                                            ${getGrammarRule(correction.error_type)}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary" onclick="showSimilarExamples()">
                                    <i class="fas fa-examples"></i> Similar Examples
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal
            const existingModal = document.getElementById('analysisModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalContent);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('analysisModal'));
            modal.show();
        }

        function getGrammarRule(errorType) {
            const rules = {
                'grammar': 'Pay attention to subject-verb agreement, verb tenses, and sentence structure.',
                'vocabulary': 'Use appropriate and formal vocabulary. Avoid informal or colloquial expressions.',
                'grammar|vocabulary': 'This error involves both grammatical structure and word choice. Focus on proper verb forms and academic vocabulary.',
                'coherence': 'Ensure logical flow and proper linking between ideas.',
                'task_response': 'Make sure to fully address all parts of the task question.'
            };
            return rules[errorType] || 'Focus on accuracy and appropriateness in your language use.';
        }

        function animateElements() {
            // Animate score
            animateNumber('overallScore', 0, 5.5, 2000);
            
            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        function animateNumber(elementId, start, end, duration) {
            const element = document.getElementById(elementId);
            const startTime = performance.now();
            
            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current = start + (end - start) * progress;
                
                element.textContent = current.toFixed(1);
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }
            
            requestAnimationFrame(updateNumber);
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Initialize demo when page loads
        document.addEventListener('DOMContentLoaded', initializeDemo);
    </script>
</body>
</html>
