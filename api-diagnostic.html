<!DOCTYPE html>
<html>
<head>
    <title>API Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body><h1>≡ƒöº API Connection Diagnostic Test</h1><h3>1. Configuration Check</h3><div class='info'><strong>API URL:</strong> https://api.v3.cm/v1/chat/completions<br><strong>API Key:</strong> sk-JaDgJ8G...9842d<br><strong>Model:</strong> gpt-4o-mini<br></div><h3>2. Simple API Connection Test</h3><div class='info'><strong>HTTP Status Code:</strong> 200<br></div><div class='success'><strong>Γ£à API Connection Successful!</strong></div><div class='success'><strong>API Response:</strong> Hello, API is working!</div><details><summary>Raw Response</summary><pre>{
  &quot;id&quot;: &quot;chatcmpl-BuM199LGMMQ1VByJJRqJjfg9tBFt6&quot;,
  &quot;object&quot;: &quot;chat.completion&quot;,
  &quot;created&quot;: 1752770703,
  &quot;model&quot;: &quot;gpt-4o-mini-2024-07-18&quot;,
  &quot;choices&quot;: [
    {
      &quot;index&quot;: 0,
      &quot;message&quot;: {
        &quot;role&quot;: &quot;assistant&quot;,
        &quot;content&quot;: &quot;Hello, API is working!&quot;,
        &quot;refusal&quot;: null,
        &quot;annotations&quot;: []
      },
      &quot;logprobs&quot;: null,
      &quot;finish_reason&quot;: &quot;stop&quot;
    }
  ],
  &quot;usage&quot;: {
    &quot;prompt_tokens&quot;: 19,
    &quot;completion_tokens&quot;: 7,
    &quot;total_tokens&quot;: 26,
    &quot;prompt_tokens_details&quot;: {
      &quot;cached_tokens&quot;: 0,
      &quot;audio_tokens&quot;: 0
    },
    &quot;completion_tokens_details&quot;: {
      &quot;reasoning_tokens&quot;: 0,
      &quot;audio_tokens&quot;: 0,
      &quot;accepted_prediction_tokens&quot;: 0,
      &quot;rejected_prediction_tokens&quot;: 0
    }
  },
  &quot;system_fingerprint&quot;: &quot;fp_efad92c60b&quot;
}
</pre></details><h3>3. IELTSScorer Class Test</h3><div class='success'>Γ£à IELTSScorer class loaded successfully</div><div class='success'><strong>Γ£à Scoring Successful!</strong><br>Band Score: 3<br>Corrections Found: 1</div><h3>4. File System Check</h3><div class='success'>Γ£à config.php exists and readable</div><div class='success'>Γ£à IELTSScorer.php exists and readable</div><div class='success'>Γ£à assets/essay-highlighter.js exists and readable</div><div class='success'>Γ£à assets/essay-highlighter.css exists and readable</div></body></html>
