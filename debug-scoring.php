<?php
require_once 'config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Debug Scoring</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .step { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
    </style>
</head>
<body>";

echo "<h1>🔍 Debug Scoring Process</h1>";

// Test essay
$testEssay = "Education is very important for people. Many students go to school to learn.";
$taskType = 'task2';
$prompt = 'Discuss the importance of education.';

echo "<div class='info'>";
echo "<h3>Test Data:</h3>";
echo "<strong>Essay:</strong> " . htmlspecialchars($testEssay) . "<br>";
echo "<strong>Task Type:</strong> " . htmlspecialchars($taskType) . "<br>";
echo "<strong>Prompt:</strong> " . htmlspecialchars($prompt) . "<br>";
echo "</div>";

try {
    // Step 1: Load IELTSScorer
    echo "<div class='step'><strong>Step 1:</strong> Loading IELTSScorer class...</div>";
    require_once 'IELTSScorer.php';
    $scorer = new IELTSScorer();
    echo "<div class='success'>✅ IELTSScorer loaded successfully</div>";
    
    // Step 2: Validate inputs
    echo "<div class='step'><strong>Step 2:</strong> Validating inputs...</div>";
    if (empty($testEssay)) {
        throw new Exception('Essay is empty');
    }
    if (empty($taskType)) {
        throw new Exception('Task type is empty');
    }
    echo "<div class='success'>✅ Inputs validated</div>";
    
    // Step 3: Generate prompt (using reflection to access private method)
    echo "<div class='step'><strong>Step 3:</strong> Generating scoring prompt...</div>";
    $reflection = new ReflectionClass($scorer);
    $generatePromptMethod = $reflection->getMethod('generateScoringPrompt');
    $generatePromptMethod->setAccessible(true);
    
    $scoringPrompt = $generatePromptMethod->invoke($scorer, $testEssay, $taskType, $prompt);
    echo "<div class='success'>✅ Scoring prompt generated</div>";
    echo "<details><summary>View Prompt</summary><pre>" . htmlspecialchars($scoringPrompt) . "</pre></details>";
    
    // Step 4: Test API call
    echo "<div class='step'><strong>Step 4:</strong> Testing API call...</div>";
    
    // Manual API call for debugging
    $data = [
        'model' => OPENAI_MODEL,
        'messages' => [
            [
                'role' => 'user',
                'content' => $scoringPrompt
            ]
        ],
        'max_tokens' => 1000, // Reduced for testing
        'temperature' => 0.3
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, OPENAI_API_URL);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . OPENAI_API_KEY,
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    if ($curlError) {
        throw new Exception('cURL Error: ' . $curlError);
    }
    
    echo "<div class='info'>HTTP Status: {$httpCode}</div>";
    
    if ($httpCode !== 200) {
        echo "<div class='error'>❌ API Error (HTTP {$httpCode})</div>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        throw new Exception('API returned HTTP ' . $httpCode);
    }
    
    echo "<div class='success'>✅ API call successful</div>";
    
    // Step 5: Parse response
    echo "<div class='step'><strong>Step 5:</strong> Parsing API response...</div>";
    
    $decodedResponse = json_decode($response, true);
    if (!$decodedResponse) {
        throw new Exception('Failed to decode JSON response');
    }
    
    if (!isset($decodedResponse['choices'][0]['message']['content'])) {
        throw new Exception('Invalid response structure');
    }
    
    $content = $decodedResponse['choices'][0]['message']['content'];
    echo "<div class='success'>✅ Response parsed successfully</div>";
    echo "<details><summary>View Raw Content</summary><pre>" . htmlspecialchars($content) . "</pre></details>";
    
    // Step 6: Test nuclear parsing
    echo "<div class='step'><strong>Step 6:</strong> Testing nuclear parsing...</div>";
    
    $parseMethod = $reflection->getMethod('parseResponse');
    $parseMethod->setAccessible(true);
    
    $parsedResult = $parseMethod->invoke($scorer, $content);
    echo "<div class='success'>✅ Nuclear parsing completed</div>";
    echo "<details><summary>View Parsed Result</summary><pre>" . htmlspecialchars(json_encode($parsedResult, JSON_PRETTY_PRINT)) . "</pre></details>";
    
    // Step 7: Full scoring test
    echo "<div class='step'><strong>Step 7:</strong> Full scoring test...</div>";
    
    $fullResult = $scorer->scoreEssay($testEssay, $taskType, $prompt);
    
    if (isset($fullResult['error']) && $fullResult['error']) {
        echo "<div class='error'>❌ Scoring failed: " . htmlspecialchars($fullResult['message']) . "</div>";
    } else {
        echo "<div class='success'>✅ Full scoring successful!</div>";
        echo "<div class='info'>";
        echo "<strong>Band Score:</strong> " . ($fullResult['overall_band_score'] ?? 'N/A') . "<br>";
        echo "<strong>Corrections:</strong> " . (isset($fullResult['highlighted_corrections']) ? count($fullResult['highlighted_corrections']) : 0) . "<br>";
        echo "</div>";
    }
    
    echo "<details><summary>View Full Result</summary><pre>" . htmlspecialchars(json_encode($fullResult, JSON_PRETTY_PRINT)) . "</pre></details>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<strong>❌ Error in Step:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<strong>Debug Info:</strong><br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "</div>";
}

echo "</body></html>";
?>
