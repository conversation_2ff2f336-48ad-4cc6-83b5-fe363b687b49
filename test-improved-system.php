<?php
require_once 'config.php';
require_once 'IELTSScorer.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Improved System Test</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: #f8f9fa; padding: 20px; }
        .container { max-width: 1000px; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1><i class='fas fa-cogs'></i> Improved System Test</h1>";
echo "<p class='lead'>Testing the enhanced error handling and retry logic</p>";

$testEssay = "Many people believes that education is very important for success in life. Students should study hard to achieve their goals and get good jobs in the future.";
$taskType = 'task2';
$prompt = 'Education is important for success. Discuss this statement.';

echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-edit'></i> Test Essay:</h5>";
echo "<p>" . htmlspecialchars($testEssay) . "</p>";
echo "<strong>Task Type:</strong> " . htmlspecialchars($taskType) . "<br>";
echo "<strong>Prompt:</strong> " . htmlspecialchars($prompt);
echo "</div>";

try {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-spinner fa-spin'></i> Testing improved scoring system...";
    echo "</div>";
    
    $startTime = microtime(true);
    $scorer = new IELTSScorer();
    $result = $scorer->scoreEssay($testEssay, $taskType, $prompt);
    $endTime = microtime(true);
    $duration = round($endTime - $startTime, 2);
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-clock'></i> Processing completed in {$duration} seconds";
    echo "</div>";
    
    if (isset($result['error']) && $result['error']) {
        echo "<div class='alert alert-danger'>";
        echo "<h4><i class='fas fa-exclamation-triangle'></i> Scoring Error</h4>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($result['message']) . "</p>";
        
        if (isset($result['debug_info'])) {
            echo "<h5>Debug Information:</h5>";
            echo "<ul>";
            foreach ($result['debug_info'] as $key => $value) {
                echo "<li><strong>" . htmlspecialchars($key) . ":</strong> " . htmlspecialchars($value) . "</li>";
            }
            echo "</ul>";
        }
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-success'>";
        echo "<h4><i class='fas fa-trophy'></i> Scoring Successful!</h4>";
        echo "<p><strong>Overall Band Score:</strong> " . ($result['overall_band_score'] ?? 'N/A') . "</p>";
        
        if (isset($result['highlighted_corrections'])) {
            $corrections = $result['highlighted_corrections'];
            $cleanCorrections = 0;
            
            foreach ($corrections as $correction) {
                if (isset($correction['original_text']) && 
                    !empty($correction['original_text']) &&
                    strpos($correction['original_text'], 'data-') === false) {
                    $cleanCorrections++;
                }
            }
            
            echo "<p><strong>Total Corrections:</strong> " . count($corrections) . "</p>";
            echo "<p><strong>Clean Corrections:</strong> {$cleanCorrections}</p>";
            
            if ($cleanCorrections > 0) {
                echo "<div class='alert alert-success'>";
                echo "<i class='fas fa-check'></i> Nuclear cleaning successful! {$cleanCorrections} clean corrections ready for highlighting.";
                echo "</div>";
                
                echo "<h5>Sample Clean Corrections:</h5>";
                $sampleCount = 0;
                foreach ($corrections as $i => $correction) {
                    if ($sampleCount >= 3) break;
                    
                    $originalText = $correction['original_text'] ?? '';
                    if (!empty($originalText) && strpos($originalText, 'data-') === false) {
                        echo "<div class='card mb-2'>";
                        echo "<div class='card-body'>";
                        echo "<strong>Original:</strong> \"" . htmlspecialchars($originalText) . "\"<br>";
                        echo "<strong>Suggested:</strong> \"" . htmlspecialchars($correction['suggested_correction'] ?? '') . "\"<br>";
                        echo "<strong>Type:</strong> " . htmlspecialchars($correction['error_type'] ?? 'N/A') . "<br>";
                        echo "</div>";
                        echo "</div>";
                        $sampleCount++;
                    }
                }
            } else {
                echo "<div class='alert alert-warning'>";
                echo "<i class='fas fa-exclamation-triangle'></i> No clean corrections found. Nuclear cleaning may need adjustment.";
                echo "</div>";
            }
        }
        
        if (isset($result['metadata'])) {
            echo "<h5>Metadata:</h5>";
            echo "<ul>";
            foreach ($result['metadata'] as $key => $value) {
                echo "<li><strong>" . htmlspecialchars($key) . ":</strong> " . htmlspecialchars($value) . "</li>";
            }
            echo "</ul>";
        }
        echo "</div>";
    }
    
    echo "<details class='mt-4'>";
    echo "<summary class='btn btn-outline-secondary'><i class='fas fa-code'></i> View Full Result</summary>";
    echo "<pre class='bg-light p-3 rounded mt-3'>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>";
    echo "</details>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4><i class='fas fa-bomb'></i> System Exception</h4>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='mt-4'>";
echo "<h5><i class='fas fa-info-circle'></i> System Status</h5>";
echo "<ul class='list-group'>";
echo "<li class='list-group-item d-flex justify-content-between align-items-center'>";
echo "Enhanced Error Handling";
echo "<span class='badge bg-success rounded-pill'>Active</span>";
echo "</li>";
echo "<li class='list-group-item d-flex justify-content-between align-items-center'>";
echo "API Retry Logic";
echo "<span class='badge bg-success rounded-pill'>Active</span>";
echo "</li>";
echo "<li class='list-group-item d-flex justify-content-between align-items-center'>";
echo "Nuclear Text Cleaning";
echo "<span class='badge bg-success rounded-pill'>Active</span>";
echo "</li>";
echo "<li class='list-group-item d-flex justify-content-between align-items-center'>";
echo "Debug Information";
echo "<span class='badge bg-success rounded-pill'>Active</span>";
echo "</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
