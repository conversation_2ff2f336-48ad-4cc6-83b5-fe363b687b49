<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IELTS Writing Scorer - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/essay-highlighter.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .demo-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .demo-highlight {
            background: rgba(255, 193, 7, 0.3);
            border-bottom: 2px wavy #ffc107;
            padding: 2px 4px;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .btn-demo {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 12px 25px;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-demo:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-container">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1><i class="fas fa-graduation-cap"></i> IELTS Writing Scorer</h1>
                <p class="lead">Comprehensive AI-powered IELTS Writing Assessment with Visual Feedback</p>
                <div class="mt-4">
                    <a href="index.php" class="btn-demo">
                        <i class="fas fa-edit"></i> Start Scoring
                    </a>
                    <a href="dashboard.php" class="btn-demo">
                        <i class="fas fa-chart-line"></i> View Dashboard
                    </a>
                    <a href="test-highlighting.php" class="btn-demo">
                        <i class="fas fa-test-tube"></i> Test Highlighting
                    </a>
                </div>
            </div>

            <!-- Features Overview -->
            <div class="row">
                <div class="col-md-6">
                    <div class="feature-card">
                        <h4><i class="fas fa-chart-bar text-primary"></i> Comprehensive Scoring</h4>
                        <p>Đánh giá theo 4 tiêu chí chính thức của IELTS:</p>
                        <ul>
                            <li><strong>Task Achievement/Response</strong> (25%)</li>
                            <li><strong>Coherence and Cohesion</strong> (25%)</li>
                            <li><strong>Lexical Resource</strong> (25%)</li>
                            <li><strong>Grammatical Range and Accuracy</strong> (25%)</li>
                        </ul>
                        <p class="text-muted">Điểm số từ 1.0 đến 9.0 với phân tích chi tiết cho từng tiêu chí.</p>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="feature-card">
                        <h4><i class="fas fa-highlighter text-warning"></i> Visual Error Highlighting</h4>
                        <p>Highlight lỗi trực quan như giáo viên chấm bài:</p>
                        <div class="mb-3">
                            <span class="demo-highlight grammar">Grammar errors</span> - 
                            <span class="demo-highlight vocabulary">Vocabulary issues</span> - 
                            <span class="demo-highlight coherence">Coherence problems</span> - 
                            <span class="demo-highlight task_response">Task response issues</span>
                        </div>
                        <p class="text-muted">Click vào lỗi để xem gợi ý sửa chi tiết với giải thích.</p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="feature-card">
                        <h4><i class="fas fa-chart-line text-success"></i> Progress Tracking</h4>
                        <p>Theo dõi tiến độ học tập qua thời gian:</p>
                        <ul>
                            <li>Biểu đồ cải thiện điểm số</li>
                            <li>Phân tích điểm mạnh/yếu</li>
                            <li>Thống kê lỗi thường gặp</li>
                            <li>Gợi ý học tập cá nhân hóa</li>
                            <li>Lịch sử bài viết đã chấm</li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="feature-card">
                        <h4><i class="fas fa-cogs text-info"></i> Smart Features</h4>
                        <p>Các tính năng thông minh hỗ trợ học tập:</p>
                        <ul>
                            <li>Đếm từ tự động với mã màu</li>
                            <li>Tự động lưu bản nháp</li>
                            <li>Export dữ liệu tiến độ</li>
                            <li>Responsive design</li>
                            <li>Ước tính thời gian cải thiện</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Sample Essay Demo -->
            <div class="feature-card">
                <h4><i class="fas fa-file-alt text-primary"></i> Sample Essay with Highlighting</h4>
                <p class="text-muted">Ví dụ về cách hệ thống highlight và gợi ý sửa lỗi:</p>
                
                <div class="highlighted-essay-container">
                    <div class="essay-content">
                        <p>
                            <span class="highlight grammar high" title="Subject-verb agreement error">Many people believes</span> 
                            that technology 
                            <span class="highlight grammar medium" title="Incorrect verb form">have changed</span> 
                            our 
                            <span class="highlight vocabulary low" title="Spelling error">lifes</span> 
                            dramatically. In my opinion, I think that 
                            <span class="highlight grammar high" title="Pronoun-verb disagreement">this changes has</span> 
                            both positive and negative effects on society.
                        </p>
                        
                        <p>
                            Firstly, technology has made communication 
                            <span class="highlight vocabulary medium" title="Incorrect comparative form">more easier</span> 
                            than before. People can now 
                            <span class="highlight vocabulary low" title="Unnecessary preposition">contact with</span> 
                            their friends and family members who live in different countries through social media platforms.
                        </p>
                        
                        <p>
                            However, there are also some disadvantages. This can 
                            <span class="highlight grammar high" title="Wrong word choice - affect vs effect">effect</span> 
                            their mental health and social skills.
                        </p>
                    </div>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i> 
                        Trong ứng dụng thực tế, bạn có thể click vào các lỗi được highlight để xem gợi ý sửa chi tiết.
                    </small>
                </div>
            </div>

            <!-- How to Use -->
            <div class="feature-card">
                <h4><i class="fas fa-play-circle text-success"></i> Cách sử dụng</h4>
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="mb-3">
                            <i class="fas fa-edit fa-3x text-primary"></i>
                        </div>
                        <h6>1. Viết bài</h6>
                        <p class="small">Chọn loại task và viết bài IELTS của bạn</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="mb-3">
                            <i class="fas fa-robot fa-3x text-warning"></i>
                        </div>
                        <h6>2. AI chấm điểm</h6>
                        <p class="small">Hệ thống AI phân tích và chấm điểm chi tiết</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="mb-3">
                            <i class="fas fa-highlighter fa-3x text-danger"></i>
                        </div>
                        <h6>3. Xem highlighting</h6>
                        <p class="small">Lỗi được highlight với gợi ý sửa cụ thể</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="mb-3">
                            <i class="fas fa-chart-line fa-3x text-success"></i>
                        </div>
                        <h6>4. Theo dõi tiến độ</h6>
                        <p class="small">Dashboard hiển thị cải thiện qua thời gian</p>
                    </div>
                </div>
            </div>

            <!-- Technical Info -->
            <div class="feature-card">
                <h4><i class="fas fa-info-circle text-info"></i> Thông tin kỹ thuật</h4>
                <div class="row">
                    <div class="col-md-6">
                        <ul>
                            <li><strong>AI Model:</strong> OpenAI GPT-4o-mini</li>
                            <li><strong>Backend:</strong> PHP 8.2+</li>
                            <li><strong>Frontend:</strong> Bootstrap 5, JavaScript ES6</li>
                            <li><strong>Charts:</strong> Chart.js</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul>
                            <li><strong>Storage:</strong> LocalStorage (client-side)</li>
                            <li><strong>Responsive:</strong> Mobile-friendly design</li>
                            <li><strong>Real-time:</strong> Instant scoring và highlighting</li>
                            <li><strong>Export:</strong> JSON format cho dữ liệu</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center mt-5">
                <h3>Sẵn sàng cải thiện IELTS Writing của bạn?</h3>
                <p class="text-muted">Bắt đầu với bài viết đầu tiên và xem sự cải thiện qua thời gian!</p>
                <a href="index.php" class="btn-demo btn-lg">
                    <i class="fas fa-rocket"></i> Bắt đầu ngay
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add some interactivity to demo highlights
        document.querySelectorAll('.demo-highlight').forEach(element => {
            element.addEventListener('click', function() {
                alert(`Demo: ${this.textContent}\nError type: ${this.className.split(' ')[1]}\nSeverity: ${this.className.split(' ')[2] || 'medium'}\n\nIn the real app, this would show a detailed correction modal.`);
            });
        });
    </script>
</body>
</html>
