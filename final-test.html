<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Highlighting Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/essay-highlighter.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; padding: 20px; }
        .test-container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #dee2e6; border-radius: 10px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-check-circle text-success"></i> Final Highlighting System Test</h1>
        <p class="lead">Comprehensive test of the IELTS highlighting and correction system</p>

        <!-- Test 1: Basic Highlighting -->
        <div class="test-section">
            <h3><i class="fas fa-play-circle"></i> Test 1: Basic Highlighting</h3>
            <p>Testing with simple corrections to verify basic functionality.</p>
            <button class="btn btn-primary" onclick="runTest1()">Run Basic Test</button>
            <div id="test1Result" class="mt-3"></div>
            <div id="test1Container" class="mt-3"></div>
        </div>

        <!-- Test 2: Complex Highlighting -->
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> Test 2: Complex Highlighting</h3>
            <p>Testing with multiple error types and overlapping corrections.</p>
            <button class="btn btn-warning" onclick="runTest2()">Run Complex Test</button>
            <div id="test2Result" class="mt-3"></div>
            <div id="test2Container" class="mt-3"></div>
        </div>

        <!-- Test 3: Edge Cases -->
        <div class="test-section">
            <h3><i class="fas fa-exclamation-triangle"></i> Test 3: Edge Cases</h3>
            <p>Testing with problematic text patterns and special characters.</p>
            <button class="btn btn-danger" onclick="runTest3()">Run Edge Case Test</button>
            <div id="test3Result" class="mt-3"></div>
            <div id="test3Container" class="mt-3"></div>
        </div>

        <!-- Console Output -->
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> Console Output</h3>
            <div id="consoleOutput" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
            <button class="btn btn-secondary btn-sm mt-2" onclick="clearConsole()">Clear Console</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/essay-highlighter.js"></script>
    <script>
        // Console override for debugging
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function logToPage(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type.toUpperCase()}: ${args.join(' ')}\n`;
            consoleOutput.textContent += message;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToPage('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToPage('error', ...args);
        };

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        function showResult(testId, success, message) {
            const resultDiv = document.getElementById(`test${testId}Result`);
            resultDiv.className = success ? 'success' : 'error';
            resultDiv.innerHTML = `<i class="fas fa-${success ? 'check' : 'times'}"></i> ${message}`;
        }

        function runTest1() {
            console.log('=== Running Test 1: Basic Highlighting ===');
            
            const essay = "Many people believes that technology have changed our lifes dramatically.";
            const corrections = [
                {
                    original_text: "people believes",
                    suggested_correction: "people believe",
                    error_type: "grammar",
                    explanation: "Subject-verb agreement error",
                    severity: "high"
                },
                {
                    original_text: "lifes",
                    suggested_correction: "lives",
                    error_type: "vocabulary",
                    explanation: "Spelling error",
                    severity: "medium"
                }
            ];

            try {
                const highlighter = new EssayHighlighter('test1Container');
                highlighter.init({ highlighted_corrections: corrections }, essay);
                showResult(1, true, 'Basic highlighting test completed successfully');
                console.log('Test 1 completed successfully');
            } catch (error) {
                showResult(1, false, `Test failed: ${error.message}`);
                console.error('Test 1 failed:', error);
            }
        }

        function runTest2() {
            console.log('=== Running Test 2: Complex Highlighting ===');
            
            const essay = `Many people believes that technology have changed our lifes dramatically. In my opinion, I think that this changes has both positive and negative effects on society.

Firstly, technology has made communication more easier than before. People can now contact with their friends and family members who live in different countries through social media platforms like Facebook and WhatsApp. This is very convenient and save time.

However, there are also some disadvantages. Many young people are becoming addicted to their smartphones and spend too much time on social media instead of studying or socializing with real people. This can effect their mental health and social skills.`;

            const corrections = [
                {
                    original_text: "people believes",
                    suggested_correction: "people believe",
                    error_type: "grammar",
                    explanation: "Subject-verb agreement error",
                    severity: "high"
                },
                {
                    original_text: "technology have changed",
                    suggested_correction: "technology has changed",
                    error_type: "grammar",
                    explanation: "Subject-verb agreement with singular subject",
                    severity: "high"
                },
                {
                    original_text: "lifes",
                    suggested_correction: "lives",
                    error_type: "vocabulary",
                    explanation: "Spelling error - plural of life",
                    severity: "medium"
                },
                {
                    original_text: "this changes has",
                    suggested_correction: "these changes have",
                    error_type: "grammar",
                    explanation: "Plural agreement error",
                    severity: "high"
                },
                {
                    original_text: "more easier",
                    suggested_correction: "easier",
                    error_type: "vocabulary",
                    explanation: "Double comparative error",
                    severity: "medium"
                },
                {
                    original_text: "contact with",
                    suggested_correction: "contact",
                    error_type: "vocabulary",
                    explanation: "Unnecessary preposition",
                    severity: "low"
                },
                {
                    original_text: "This can effect",
                    suggested_correction: "This can affect",
                    error_type: "vocabulary",
                    explanation: "Affect vs effect confusion",
                    severity: "high"
                }
            ];

            try {
                const highlighter = new EssayHighlighter('test2Container');
                highlighter.init({ highlighted_corrections: corrections }, essay);
                showResult(2, true, `Complex highlighting test completed with ${corrections.length} corrections`);
                console.log('Test 2 completed successfully');
            } catch (error) {
                showResult(2, false, `Test failed: ${error.message}`);
                console.error('Test 2 failed:', error);
            }
        }

        function runTest3() {
            console.log('=== Running Test 3: Edge Cases ===');
            
            const essay = `Education is "important" than ever & students should focus on it. Don't you think so?`;
            const corrections = [
                {
                    original_text: "important\" than",
                    suggested_correction: "more important than",
                    error_type: "grammar",
                    explanation: "Missing comparative form",
                    severity: "high"
                },
                {
                    original_text: "Don't you think",
                    suggested_correction: "Do you not think",
                    error_type: "coherence",
                    explanation: "Formal writing style",
                    severity: "low"
                }
            ];

            try {
                const highlighter = new EssayHighlighter('test3Container');
                highlighter.init({ highlighted_corrections: corrections }, essay);
                showResult(3, true, 'Edge case test completed - special characters handled');
                console.log('Test 3 completed successfully');
            } catch (error) {
                showResult(3, false, `Test failed: ${error.message}`);
                console.error('Test 3 failed:', error);
            }
        }

        // Auto-run basic test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded - Final highlighting test ready');
            console.log('Click the test buttons to run different scenarios');
        });
    </script>
</body>
</html>
