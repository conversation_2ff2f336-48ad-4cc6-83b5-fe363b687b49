<?php
require_once 'config.php';
require_once 'IELTSScorer.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Final API Test - Complete Solution</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <link href='assets/essay-highlighter.css' rel='stylesheet'>
    <style>
        body { background: #f8f9fa; padding: 20px; }
        .container { max-width: 1200px; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .correction { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #dee2e6; border-radius: 10px; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1><i class='fas fa-rocket'></i> Final API Test - Complete Solution</h1>";
echo "<p class='lead'>Testing the complete highlighting system with ultra-aggressive cleaning</p>";

$testEssay = "Many people believes that education is important than ever. Students should only learn knowledge from books to do well in exam, and not waste time on skill like cooking or dressing.";
$taskType = 'task2';
$prompt = 'Education is important. Discuss the role of books versus practical skills in learning.';

try {
    echo "<div class='test-section'>";
    echo "<h3><i class='fas fa-edit'></i> Test Essay</h3>";
    echo "<div class='alert alert-info'>" . htmlspecialchars($testEssay) . "</div>";
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h3><i class='fas fa-cog'></i> API Processing</h3>";
    echo "<p>Calling OpenAI API with ultra-aggressive cleaning...</p>";
    
    $scorer = new IELTSScorer();
    $result = $scorer->scoreEssay($testEssay, $taskType, $prompt);
    
    if (isset($result['error']) && $result['error']) {
        echo "<div class='error'><i class='fas fa-times'></i> Error: " . htmlspecialchars($result['message']) . "</div>";
    } else {
        echo "<div class='success'><i class='fas fa-check'></i> API call successful! Overall score: " . ($result['overall_band_score'] ?? 'N/A') . "</div>";
        
        if (isset($result['highlighted_corrections']) && is_array($result['highlighted_corrections'])) {
            echo "<h4>Corrections Analysis (" . count($result['highlighted_corrections']) . " found):</h4>";
            
            $allClean = true;
            $validCorrections = 0;
            
            foreach ($result['highlighted_corrections'] as $i => $correction) {
                $originalText = $correction['original_text'] ?? '';
                $suggestedText = $correction['suggested_correction'] ?? '';
                $explanation = $correction['explanation'] ?? '';
                
                // Check for HTML artifacts
                $hasHtml = (strpos($originalText, '<') !== false || 
                           strpos($originalText, 'data-') !== false ||
                           strpos($suggestedText, '<') !== false || 
                           strpos($suggestedText, 'data-') !== false ||
                           strpos($explanation, '<') !== false || 
                           strpos($explanation, 'data-') !== false);
                
                if (!$hasHtml && !empty($originalText) && !empty($suggestedText)) {
                    $validCorrections++;
                    echo "<div class='correction'>";
                    echo "<h5><i class='fas fa-check-circle text-success'></i> Correction " . ($i + 1) . " - CLEAN</h5>";
                    echo "<strong>Original:</strong> \"" . htmlspecialchars($originalText) . "\"<br>";
                    echo "<strong>Suggested:</strong> \"" . htmlspecialchars($suggestedText) . "\"<br>";
                    echo "<strong>Type:</strong> " . htmlspecialchars($correction['error_type'] ?? 'N/A') . "<br>";
                    echo "<strong>Severity:</strong> " . htmlspecialchars($correction['severity'] ?? 'N/A') . "<br>";
                    echo "<strong>Explanation:</strong> " . htmlspecialchars($explanation) . "<br>";
                    echo "<div class='text-success'><i class='fas fa-thumbs-up'></i> Ready for highlighting</div>";
                    echo "</div>";
                } else {
                    $allClean = false;
                    echo "<div class='correction' style='border-left-color: #dc3545;'>";
                    echo "<h5><i class='fas fa-exclamation-triangle text-danger'></i> Correction " . ($i + 1) . " - PROBLEMATIC</h5>";
                    echo "<strong>Original:</strong> \"" . htmlspecialchars($originalText) . "\"<br>";
                    echo "<strong>Suggested:</strong> \"" . htmlspecialchars($suggestedText) . "\"<br>";
                    echo "<div class='text-danger'><i class='fas fa-times'></i> Contains HTML artifacts or empty</div>";
                    echo "</div>";
                }
            }
            
            // Summary
            echo "<div class='test-section'>";
            echo "<h3><i class='fas fa-chart-bar'></i> Cleaning Results Summary</h3>";
            if ($validCorrections > 0) {
                echo "<div class='success'>";
                echo "<h4><i class='fas fa-trophy'></i> SUCCESS!</h4>";
                echo "<p><strong>{$validCorrections}</strong> out of " . count($result['highlighted_corrections']) . " corrections are clean and ready for highlighting.</p>";
                echo "</div>";
            } else {
                echo "<div class='error'>";
                echo "<h4><i class='fas fa-exclamation-triangle'></i> FAILED!</h4>";
                echo "<p>No clean corrections found. All corrections contain HTML artifacts.</p>";
                echo "</div>";
            }
            echo "</div>";
            
            // Test highlighting if we have clean data
            if ($validCorrections > 0) {
                echo "<div class='test-section'>";
                echo "<h3><i class='fas fa-highlighter'></i> Highlighting Test</h3>";
                echo "<p>Testing the highlighting system with clean data...</p>";
                echo "<div id='highlightingTest'></div>";
                echo "<button class='btn btn-primary' onclick='testHighlighting()'><i class='fas fa-play'></i> Test Highlighting</button>";
                echo "</div>";
            }
            
        } else {
            echo "<div class='error'>No highlighted_corrections found in response</div>";
        }
    }
    
    echo "<details class='mt-4'>";
    echo "<summary><i class='fas fa-code'></i> Raw API Response (for debugging)</summary>";
    echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>";
    echo "</details>";
    
} catch (Exception $e) {
    echo "<div class='error'><i class='fas fa-bug'></i> Exception: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div>";

echo "
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
<script src='assets/essay-highlighter.js'></script>
<script>
function testHighlighting() {
    const result = " . json_encode($result ?? []) . ";
    const essay = " . json_encode($testEssay) . ";
    
    console.log('Testing highlighting with cleaned data:', result);
    
    if (result && result.highlighted_corrections && essay) {
        try {
            const highlighter = new EssayHighlighter('highlightingTest');
            highlighter.init(result, essay);
            
            // Show success message
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success mt-3';
            successDiv.innerHTML = '<i class=\"fas fa-check\"></i> Highlighting test completed successfully!';
            document.getElementById('highlightingTest').appendChild(successDiv);
            
        } catch (error) {
            console.error('Highlighting failed:', error);
            document.getElementById('highlightingTest').innerHTML = 
                '<div class=\"alert alert-danger\"><i class=\"fas fa-times\"></i> Highlighting failed: ' + error.message + '</div>';
        }
    } else {
        document.getElementById('highlightingTest').innerHTML = 
            '<div class=\"alert alert-warning\"><i class=\"fas fa-exclamation-triangle\"></i> No valid data for highlighting test</div>';
    }
}
</script>
</body>
</html>";
?>
