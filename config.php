<?php
/**
 * Configuration file for IELTS Writing Scorer
 */

// API Configuration
define('OPENAI_API_URL', 'https://api.v3.cm/v1/chat/completions');
define('OPENAI_API_KEY', 'sk-JaDgJ8GwSYShL5uUB692D06073774f12A1F0Cc767839842d');
define('OPENAI_MODEL', 'gpt-4o-mini');

// IELTS Scoring Configuration
define('MAX_TOKENS', 3980);
define('TEMPERATURE', 0.3); // Lower for more consistent scoring
define('TOP_P', 1);
define('PRESENCE_PENALTY', 0);
define('FREQUENCY_PENALTY', 0);

// IELTS Band Score Ranges
define('BAND_SCORES', [
    9.0 => 'Expert User',
    8.5 => 'Very Good User',
    8.0 => 'Very Good User', 
    7.5 => 'Good User',
    7.0 => 'Good User',
    6.5 => 'Competent User',
    6.0 => 'Competent User',
    5.5 => 'Modest User',
    5.0 => 'Modest User',
    4.5 => 'Limited User',
    4.0 => 'Limited User',
    3.5 => 'Extremely Limited User',
    3.0 => 'Extremely Limited User',
    2.5 => 'Intermittent User',
    2.0 => 'Intermittent User',
    1.5 => 'Non User',
    1.0 => 'Non User'
]);

// IELTS Writing Task Types
define('TASK_TYPES', [
    'task1_academic' => 'Task 1 Academic (Graph/Chart/Diagram)',
    'task1_general' => 'Task 1 General Training (Letter)',
    'task2' => 'Task 2 (Essay)'
]);

// Scoring Criteria Weights
define('SCORING_CRITERIA', [
    'task_achievement' => [
        'weight' => 0.25,
        'name' => 'Task Achievement/Response',
        'description' => 'How well the task requirements are fulfilled'
    ],
    'coherence_cohesion' => [
        'weight' => 0.25,
        'name' => 'Coherence and Cohesion',
        'description' => 'Logical organization and linking of ideas'
    ],
    'lexical_resource' => [
        'weight' => 0.25,
        'name' => 'Lexical Resource',
        'description' => 'Range and accuracy of vocabulary'
    ],
    'grammatical_range' => [
        'weight' => 0.25,
        'name' => 'Grammatical Range and Accuracy',
        'description' => 'Variety and correctness of grammar structures'
    ]
]);

// Database Configuration (if needed for storing results)
define('DB_HOST', 'localhost');
define('DB_NAME', 'ielts_scorer');
define('DB_USER', 'root');
define('DB_PASS', '');

// Application Settings
define('APP_NAME', 'IELTS Writing Scorer');
define('APP_VERSION', '1.0.0');
define('DEBUG_MODE', true);

// Error Reporting
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Timezone
date_default_timezone_set('Asia/Ho_Chi_Minh');
?>
