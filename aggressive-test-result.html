<!DOCTYPE html>
<html>
<head>
    <title>Aggressive Cleaning Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .dirty { background: #ffe6e6; }
        .clean { background: #e6ffe6; }
        .result { font-weight: bold; color: #007bff; }
    </style>
</head>
<body><h1>Aggressive Text Cleaning Test</h1><div class='test'><h3>Test Case 1</h3><div class='dirty'><strong>Original:</strong><br>data-correction-id=&quot;correction-6&quot; data-original=&quot;education is important than ever&quot; data-suggestion=&quot;education is more important than ever&quot; data-explanation=&quot;Comparative form is incorrect; &#039;more&#039; is needed.&quot; data-type=&quot;grammar&quot; data-severity=&quot;high&quot; title=&quot;Click for suggestion&quot;&gt;education is important than ever</div><div class='clean'><strong>Cleaned:</strong><br>more&#039; is needed.&quot; education is important than ever</div><div style='color: green;'>Γ£à Successfully cleaned</div><div class='result'>Length: 50 characters</div></div><div class='test'><h3>Test Case 2</h3><div class='dirty'><strong>Original:</strong><br>data-correction-id=&quot;correction-2&quot; data-original=&quot;students should only learn knowledge from books&quot; data-suggestion=&quot;students should primarily learn knowledge from books&quot; data-explanation=&quot;The word &#039;only&#039; implies exclusivity, which is not the intended meaning.&quot; data-type=&quot;vocabulary&quot; data-severity=&quot;medium&quot; title=&quot;Click for suggestion&quot;&gt;students should only learn knowledge from books</div><div class='clean'><strong>Cleaned:</strong><br>only&#039; implies exclusivity, which is not the intended meaning.&quot; students should only learn knowledge from books</div><div style='color: green;'>Γ£à Successfully cleaned</div><div class='result'>Length: 110 characters</div></div><div class='test'><h3>Test Case 3</h3><div class='dirty'><strong>Original:</strong><br>&lt;span class=&quot;highlight grammar high&quot;&gt;people believes&lt;/span&gt;</div><div class='clean'><strong>Cleaned:</strong><br>people believes</div><div style='color: green;'>Γ£à Successfully cleaned</div><div class='result'>Length: 15 characters</div></div><div class='test'><h3>Test Case 4</h3><div class='dirty'><strong>Original:</strong><br>Normal text without any HTML</div><div class='clean'><strong>Cleaned:</strong><br>Normal text without any HTML</div><div style='color: green;'>Γ£à Successfully cleaned</div><div class='result'>Length: 28 characters</div></div><div class='test'><h3>Test Case 5</h3><div class='dirty'><strong>Original:</strong><br>data-type=&quot;grammar&quot;&gt;education is important</div><div class='clean'><strong>Cleaned:</strong><br>education is important</div><div style='color: green;'>Γ£à Successfully cleaned</div><div class='result'>Length: 22 characters</div></div><div class='test'><h3>Test Case 6</h3><div class='dirty'><strong>Original:</strong><br>title=&quot;Click for suggestion&quot;&gt;some text here</div><div class='clean'><strong>Cleaned:</strong><br>some text here</div><div style='color: green;'>Γ£à Successfully cleaned</div><div class='result'>Length: 14 characters</div></div><div class='test'><h3>Test Case 7</h3><div class='dirty'><strong>Original:</strong><br>Multiple data-attr=&quot;value1&quot; data-other=&quot;value2&quot; title=&quot;test&quot;&gt;final text content</div><div class='clean'><strong>Cleaned:</strong><br>Multiple final text content</div><div style='color: green;'>Γ£à Successfully cleaned</div><div class='result'>Length: 27 characters</div></div></body></html>
