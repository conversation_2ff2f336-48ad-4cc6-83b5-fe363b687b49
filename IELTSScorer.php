<?php
require_once 'config.php';

/**
 * IELTS Writing Scorer Class
 * Provides comprehensive scoring for IELTS Writing tasks using OpenAI API
 */
class IELTSScorer {
    
    private $apiUrl;
    private $apiKey;
    private $model;
    
    public function __construct() {
        $this->apiUrl = OPENAI_API_URL;
        $this->apiKey = OPENAI_API_KEY;
        $this->model = OPENAI_MODEL;
    }
    
    /**
     * Score an IELTS writing task
     * 
     * @param string $essay The essay text to score
     * @param string $taskType Type of task (task1_academic, task1_general, task2)
     * @param string $prompt The original task prompt
     * @return array Detailed scoring results
     */
    public function scoreEssay($essay, $taskType, $prompt = '') {
        try {
            // Validate input
            if (empty($essay) || empty($taskType)) {
                throw new Exception('Essay text and task type are required');
            }
            
            // Generate scoring prompt
            $scoringPrompt = $this->generateScoringPrompt($essay, $taskType, $prompt);
            
            // Call OpenAI API
            $response = $this->callOpenAI($scoringPrompt);
            
            // Parse and structure the response
            $scoringResult = $this->parseResponse($response);
            
            // Add metadata
            $scoringResult['metadata'] = [
                'task_type' => $taskType,
                'word_count' => str_word_count($essay),
                'scored_at' => date('Y-m-d H:i:s'),
                'essay_length' => strlen($essay)
            ];
            
            return $scoringResult;
            
        } catch (Exception $e) {
            // Enhanced error logging and handling
            $errorMessage = $e->getMessage();
            $errorDetails = [
                'error' => true,
                'message' => $errorMessage,
                'scored_at' => date('Y-m-d H:i:s'),
                'debug_info' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'essay_length' => strlen($essay),
                    'task_type' => $taskType
                ]
            ];

            // Log error for debugging (if debug mode is on)
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("IELTS Scorer Error: " . $errorMessage . " in " . $e->getFile() . " on line " . $e->getLine());
            }

            return $errorDetails;
        }
    }
    
    /**
     * Generate comprehensive scoring prompt for OpenAI
     */
    private function generateScoringPrompt($essay, $taskType, $prompt) {
        $taskInstructions = $this->getTaskInstructions($taskType);

        $scoringPrompt = "
You are an expert IELTS examiner with 20+ years of experience. Please provide a comprehensive and accurate assessment of this IELTS Writing {$taskType} response.

TASK PROMPT: {$prompt}

STUDENT'S RESPONSE:
{$essay}

Please evaluate this essay according to the official IELTS Writing assessment criteria and provide your response in the following JSON format:

{
    \"overall_band_score\": 7.0,
    \"criteria_scores\": {
        \"task_achievement\": {
            \"score\": 7.0,
            \"analysis\": \"Detailed analysis of how well the task requirements are met...\",
            \"strengths\": [\"List of specific strengths\"],
            \"weaknesses\": [\"List of specific areas for improvement\"],
            \"suggestions\": [\"Specific actionable suggestions\"]
        },
        \"coherence_cohesion\": {
            \"score\": 7.0,
            \"analysis\": \"Analysis of organization and linking...\",
            \"strengths\": [\"List of strengths\"],
            \"weaknesses\": [\"List of weaknesses\"],
            \"suggestions\": [\"Specific suggestions\"]
        },
        \"lexical_resource\": {
            \"score\": 7.0,
            \"analysis\": \"Analysis of vocabulary usage...\",
            \"strengths\": [\"List of strengths\"],
            \"weaknesses\": [\"List of weaknesses\"],
            \"suggestions\": [\"Specific suggestions\"]
        },
        \"grammatical_range\": {
            \"score\": 7.0,
            \"analysis\": \"Analysis of grammar usage...\",
            \"strengths\": [\"List of strengths\"],
            \"weaknesses\": [\"List of weaknesses\"],
            \"suggestions\": [\"Specific suggestions\"]
        }
    },
    \"detailed_feedback\": {
        \"introduction\": \"Analysis of introduction paragraph\",
        \"body_paragraphs\": [\"Analysis of each body paragraph\"],
        \"conclusion\": \"Analysis of conclusion\",
        \"overall_structure\": \"Overall structural analysis\"
    },
    \"improvement_priorities\": [
        \"Most important areas to focus on for improvement\"
    ],
    \"estimated_study_time\": \"Estimated time needed to reach next band level\",
    \"sample_improvements\": {
        \"vocabulary\": [\"Better word choices with examples\"],
        \"grammar\": [\"Grammar improvements with examples\"],
        \"structure\": [\"Structural improvements\"]
    },
    \"highlighted_corrections\": [
        {
            \"original_text\": \"exact text from essay that needs correction\",
            \"suggested_correction\": \"improved version of the text\",
            \"error_type\": \"grammar|vocabulary|coherence|task_response\",
            \"explanation\": \"why this needs to be changed\",
            \"severity\": \"high|medium|low\"
        }
    ],
    \"annotated_essay\": \"The original essay with inline corrections and comments marked with [CORRECTION: original -> suggested] and [COMMENT: explanation]\"
}

{$taskInstructions}

You are a JSON API that returns ONLY valid JSON. No other text allowed.

STRICT RULES:
1. Return ONLY JSON - no markdown, no explanations, no extra text
2. ALL text fields must be plain text - NO HTML, NO markup, NO attributes
3. Copy exact words from the essay for original_text
4. Never use <, >, data-, class=, or any HTML syntax

EXACT FORMAT REQUIRED:
{
  \"overall_band_score\": 6.5,
  \"highlighted_corrections\": [
    {
      \"original_text\": \"people believes\",
      \"suggested_correction\": \"people believe\",
      \"error_type\": \"grammar\",
      \"explanation\": \"Subject-verb agreement error\",
      \"severity\": \"high\"
    }
  ]
}

EXAMPLE - CORRECT:
\"original_text\": \"education is important than ever\"

EXAMPLE - WRONG (NEVER DO THIS):
\"original_text\": \"data-original=\\\"education is important than ever\\\" title=\\\"Click\\\">education is important than ever\"

Find 5-10 errors. Use ONLY plain text.

Be extremely thorough, specific, and constructive in your feedback. Provide concrete examples from the text and specific suggestions for improvement.
";
        
        return $scoringPrompt;
    }
    
    /**
     * Get task-specific instructions
     */
    private function getTaskInstructions($taskType) {
        switch ($taskType) {
            case 'task1_academic':
                return "
TASK 1 ACADEMIC SPECIFIC CRITERIA:
- Minimum 150 words
- Describe visual information accurately
- Identify key trends and features
- Make relevant comparisons
- Use appropriate academic vocabulary
- Avoid personal opinions
";
            case 'task1_general':
                return "
TASK 1 GENERAL TRAINING SPECIFIC CRITERIA:
- Minimum 150 words
- Address all bullet points in the prompt
- Use appropriate tone (formal/informal/semi-formal)
- Follow letter format conventions
- Express purpose clearly
";
            case 'task2':
                return "
TASK 2 SPECIFIC CRITERIA:
- Minimum 250 words
- Present clear position on the topic
- Support arguments with examples
- Address all parts of the question
- Demonstrate critical thinking
- Use formal academic style
";
            default:
                return "";
        }
    }
    
    /**
     * Call OpenAI API with retry logic
     */
    private function callOpenAI($prompt) {
        $data = [
            'model' => $this->model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => MAX_TOKENS,
            'temperature' => TEMPERATURE,
            'top_p' => TOP_P,
            'presence_penalty' => PRESENCE_PENALTY,
            'frequency_penalty' => FREQUENCY_PENALTY
        ];

        $maxRetries = 3;
        $retryDelay = 1; // seconds

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $this->apiKey,
                    'Content-Type: application/json'
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 60);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $curlError = curl_error($ch);
                curl_close($ch);

                if ($curlError) {
                    throw new Exception('Network Error: ' . $curlError);
                }

                if ($httpCode === 200) {
                    $decodedResponse = json_decode($response, true);

                    if (!$decodedResponse) {
                        throw new Exception('Invalid JSON response from API');
                    }

                    if (!isset($decodedResponse['choices'][0]['message']['content'])) {
                        throw new Exception('Unexpected API response structure');
                    }

                    return $decodedResponse['choices'][0]['message']['content'];

                } elseif ($httpCode === 429) {
                    // Rate limit - wait longer before retry
                    if ($attempt < $maxRetries) {
                        sleep($retryDelay * 2);
                        continue;
                    }
                    throw new Exception('API rate limit exceeded. Please try again later.');

                } elseif ($httpCode >= 500) {
                    // Server error - retry
                    if ($attempt < $maxRetries) {
                        sleep($retryDelay);
                        continue;
                    }
                    throw new Exception('API server error (HTTP ' . $httpCode . '). Please try again later.');

                } else {
                    // Client error - don't retry
                    $errorMsg = 'API Error (HTTP ' . $httpCode . ')';
                    if ($response) {
                        $errorData = json_decode($response, true);
                        if (isset($errorData['error']['message'])) {
                            $errorMsg .= ': ' . $errorData['error']['message'];
                        }
                    }
                    throw new Exception($errorMsg);
                }

            } catch (Exception $e) {
                if ($attempt === $maxRetries) {
                    throw $e;
                }
                // Wait before retry
                sleep($retryDelay);
            }

            $retryDelay *= 2; // Exponential backoff
        }

        throw new Exception('API call failed after ' . $maxRetries . ' attempts');
    }
    
    /**
     * Parse OpenAI response and structure the data - NUCLEAR OPTION
     */
    private function parseResponse($response) {
        // Try to extract JSON from the response
        $jsonStart = strpos($response, '{');
        $jsonEnd = strrpos($response, '}');

        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonString = substr($response, $jsonStart, $jsonEnd - $jsonStart + 1);
            $parsed = json_decode($jsonString, true);

            if ($parsed) {
                // NUCLEAR CLEANING - completely rebuild the response
                $cleaned = $this->nuclearCleanResponse($parsed);
                return $cleaned;
            }
        }

        // Fallback: return raw response if JSON parsing fails
        return [
            'error' => false,
            'raw_response' => $response,
            'overall_band_score' => 0,
            'message' => 'Response received but could not parse structured data'
        ];
    }

    /**
     * Nuclear option - completely rebuild response with guaranteed clean text
     */
    private function nuclearCleanResponse($data) {
        $cleanedData = [
            'overall_band_score' => $data['overall_band_score'] ?? 0,
            'highlighted_corrections' => [],
            'annotated_essay' => $this->ultraCleanText($data['annotated_essay'] ?? ''),
            'error' => false
        ];

        if (isset($data['highlighted_corrections']) && is_array($data['highlighted_corrections'])) {
            foreach ($data['highlighted_corrections'] as $correction) {
                $originalText = $this->extractPureText($correction['original_text'] ?? '');
                $suggestedText = $this->extractPureText($correction['suggested_correction'] ?? '');
                $explanation = $this->extractPureText($correction['explanation'] ?? '');

                // Only include if we have valid clean text
                if (!empty($originalText) && !empty($suggestedText) && strlen($originalText) > 2) {
                    $cleanedData['highlighted_corrections'][] = [
                        'original_text' => $originalText,
                        'suggested_correction' => $suggestedText,
                        'error_type' => $correction['error_type'] ?? 'grammar',
                        'explanation' => $explanation,
                        'severity' => $correction['severity'] ?? 'medium'
                    ];
                }
            }
        }

        return $cleanedData;
    }

    /**
     * Extract pure text - GUARANTEED clean
     */
    private function extractPureText($text) {
        if (!$text) return '';

        // Step 1: If it looks like HTML, extract the actual text content
        if (strpos($text, 'data-') !== false || strpos($text, '<') !== false) {
            // Method 1: Extract text after the last '>'
            if (preg_match('/>[^<>]*$/', $text, $matches)) {
                $extracted = trim(str_replace('>', '', $matches[0]));
                if (!empty($extracted) && strpos($extracted, 'data-') === false) {
                    $text = $extracted;
                }
            }

            // Method 2: If still dirty, look for quoted text that makes sense
            if (strpos($text, 'data-') !== false) {
                if (preg_match('/data-original="([^"]*)"/', $text, $matches)) {
                    if (!empty($matches[1]) && strlen($matches[1]) > 2) {
                        $text = $matches[1];
                    }
                }
            }

            // Method 3: Last resort - split and find the longest meaningful part
            if (strpos($text, 'data-') !== false || strpos($text, '<') !== false) {
                $parts = preg_split('/[<>"]/', $text);
                $longest = '';
                foreach ($parts as $part) {
                    $part = trim($part);
                    if (strlen($part) > strlen($longest) &&
                        strpos($part, 'data-') === false &&
                        strpos($part, '=') === false &&
                        strlen($part) > 2) {
                        $longest = $part;
                    }
                }
                if (!empty($longest)) {
                    $text = $longest;
                }
            }
        }

        // Step 2: Standard cleaning
        $text = strip_tags($text);
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        return $text;
    }

    /**
     * Clean response data to remove HTML artifacts - ULTRA AGGRESSIVE
     */
    private function cleanResponseData($data) {
        if (isset($data['highlighted_corrections']) && is_array($data['highlighted_corrections'])) {
            foreach ($data['highlighted_corrections'] as &$correction) {
                // Ultra clean text fields
                if (isset($correction['original_text'])) {
                    $correction['original_text'] = $this->ultraCleanText($correction['original_text']);
                }
                if (isset($correction['suggested_correction'])) {
                    $correction['suggested_correction'] = $this->ultraCleanText($correction['suggested_correction']);
                }
                if (isset($correction['explanation'])) {
                    $correction['explanation'] = $this->ultraCleanText($correction['explanation']);
                }

                // Validate that we have clean text
                if (empty($correction['original_text']) ||
                    strpos($correction['original_text'], 'data-') !== false ||
                    strpos($correction['original_text'], '<') !== false) {
                    // If still dirty, try to extract from context or skip
                    $correction['original_text'] = $this->extractCleanText($correction['original_text']);
                }
            }

            // Remove any corrections that are still dirty
            $data['highlighted_corrections'] = array_filter($data['highlighted_corrections'], function($correction) {
                return !empty($correction['original_text']) &&
                       strpos($correction['original_text'], 'data-') === false &&
                       strpos($correction['original_text'], '<') === false;
            });

            // Re-index array
            $data['highlighted_corrections'] = array_values($data['highlighted_corrections']);
        }

        // Clean annotated essay
        if (isset($data['annotated_essay'])) {
            $data['annotated_essay'] = $this->ultraCleanText($data['annotated_essay']);
        }

        return $data;
    }

    /**
     * Ultra aggressive text cleaning
     */
    private function ultraCleanText($text) {
        if (!$text) return '';

        // First pass: standard cleaning
        $cleaned = $this->cleanTextContent($text);

        // If still contains artifacts, do manual extraction
        if (strpos($cleaned, 'data-') !== false || strpos($cleaned, '="') !== false) {
            $cleaned = $this->extractCleanText($text);
        }

        return $cleaned;
    }

    /**
     * Extract clean text from HTML mess - last resort
     */
    private function extractCleanText($text) {
        if (!$text) return '';

        // Method 1: Look for text after the last '>' character
        if (preg_match('/>[^<]*$/', $text, $matches)) {
            $extracted = trim(str_replace('>', '', $matches[0]));
            if (!empty($extracted) && strpos($extracted, 'data-') === false) {
                return $extracted;
            }
        }

        // Method 2: Look for text between quotes that looks like actual content
        if (preg_match_all('/"([^"]*)"/', $text, $matches)) {
            foreach ($matches[1] as $match) {
                if (strlen($match) > 3 && strpos($match, 'data-') === false && strpos($match, 'correction') === false) {
                    return $match;
                }
            }
        }

        // Method 3: Split by common delimiters and find the longest clean part
        $parts = preg_split('/[<>"]/', $text);
        $cleanest = '';
        foreach ($parts as $part) {
            $part = trim($part);
            if (strlen($part) > strlen($cleanest) &&
                strpos($part, 'data-') === false &&
                strpos($part, '=') === false &&
                !empty($part)) {
                $cleanest = $part;
            }
        }

        return $cleanest;
    }

    /**
     * Clean text content from HTML artifacts - AGGRESSIVE CLEANING
     */
    private function cleanTextContent($text) {
        if (!$text) return '';

        // Step 1: Remove all HTML tags completely
        $text = strip_tags($text);

        // Step 2: Decode HTML entities
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Step 3: Remove ALL data attributes (aggressive pattern)
        $text = preg_replace('/data-[a-zA-Z0-9-]+=["\'"][^"\']*["\'"]/', '', $text);
        $text = preg_replace('/data-[a-zA-Z0-9-]+=["\'"][^"\']*["\'"]/', '', $text); // Run twice for nested

        // Step 4: Remove title, class, id attributes
        $text = preg_replace('/title=["\'"][^"\']*["\'"]/', '', $text);
        $text = preg_replace('/class=["\'"][^"\']*["\'"]/', '', $text);
        $text = preg_replace('/id=["\'"][^"\']*["\'"]/', '', $text);

        // Step 5: Remove ANY remaining HTML-like attributes
        $text = preg_replace('/[a-zA-Z0-9-]+=["\'"][^"\']*["\'"]/', '', $text);

        // Step 6: Remove orphaned symbols from HTML cleanup
        $text = preg_replace('/^\s*[>]+\s*/', '', $text);
        $text = preg_replace('/\s*[>]+\s*$/', '', $text);
        $text = preg_replace('/\s*[>]+\s*/', ' ', $text);

        // Step 7: Remove any remaining HTML-like patterns
        $text = preg_replace('/<[^>]*>/', '', $text);

        // Step 8: Extract only the actual text content
        // Look for patterns like: 'attribute="value">actual text'
        if (preg_match('/["\']>\s*([^<]+)/', $text, $matches)) {
            $text = $matches[1];
        }

        // Step 9: Clean up extra whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        // Step 10: If still contains HTML artifacts, extract text manually
        if (strpos($text, 'data-') !== false || strpos($text, '="') !== false) {
            // Last resort: extract text after the last '>' or '"'
            $parts = preg_split('/[>"]/', $text);
            $text = end($parts);
            $text = trim($text);
        }

        return $text;
    }
    
    /**
     * Get band score description
     */
    public function getBandDescription($score) {
        $bandScores = BAND_SCORES;

        // Convert score to string for comparison
        $scoreStr = number_format($score, 1);

        // Find the closest band score
        $closestScore = '1.0';
        foreach (array_keys($bandScores) as $band) {
            if ($score >= floatval($band)) {
                $closestScore = $band;
                break;
            }
        }

        return $bandScores[$closestScore];
    }
    
    /**
     * Calculate overall band score from criteria scores
     */
    public function calculateOverallScore($criteriaScores) {
        $total = 0;
        $count = 0;
        
        foreach (SCORING_CRITERIA as $criterion => $config) {
            if (isset($criteriaScores[$criterion]['score'])) {
                $total += $criteriaScores[$criterion]['score'] * $config['weight'];
                $count++;
            }
        }
        
        if ($count === 0) return 0;
        
        $average = $total / ($count * 0.25); // Since all weights are 0.25
        
        // Round to nearest 0.5
        return round($average * 2) / 2;
    }
}
?>
