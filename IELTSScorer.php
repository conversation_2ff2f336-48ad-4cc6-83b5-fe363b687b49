<?php
require_once 'config.php';

/**
 * IELTS Writing Scorer Class
 * Provides comprehensive scoring for IELTS Writing tasks using OpenAI API
 */
class IELTSScorer {
    
    private $apiUrl;
    private $apiKey;
    private $model;
    
    public function __construct() {
        $this->apiUrl = OPENAI_API_URL;
        $this->apiKey = OPENAI_API_KEY;
        $this->model = OPENAI_MODEL;
    }
    
    /**
     * Score an IELTS writing task
     * 
     * @param string $essay The essay text to score
     * @param string $taskType Type of task (task1_academic, task1_general, task2)
     * @param string $prompt The original task prompt
     * @return array Detailed scoring results
     */
    public function scoreEssay($essay, $taskType, $prompt = '') {
        try {
            // Validate input
            if (empty($essay) || empty($taskType)) {
                throw new Exception('Essay text and task type are required');
            }
            
            // Generate scoring prompt
            $scoringPrompt = $this->generateScoringPrompt($essay, $taskType, $prompt);
            
            // Call OpenAI API
            $response = $this->callOpenAI($scoringPrompt);
            
            // Parse and structure the response
            $scoringResult = $this->parseResponse($response);
            
            // Add metadata
            $scoringResult['metadata'] = [
                'task_type' => $taskType,
                'word_count' => str_word_count($essay),
                'scored_at' => date('Y-m-d H:i:s'),
                'essay_length' => strlen($essay)
            ];
            
            return $scoringResult;
            
        } catch (Exception $e) {
            return [
                'error' => true,
                'message' => $e->getMessage(),
                'scored_at' => date('Y-m-d H:i:s')
            ];
        }
    }
    
    /**
     * Generate comprehensive scoring prompt for OpenAI
     */
    private function generateScoringPrompt($essay, $taskType, $prompt) {
        $taskInstructions = $this->getTaskInstructions($taskType);
        
        $scoringPrompt = "
You are an expert IELTS examiner with 20+ years of experience. Please provide a comprehensive and accurate assessment of this IELTS Writing {$taskType} response.

TASK PROMPT: {$prompt}

STUDENT'S RESPONSE:
{$essay}

Please evaluate this essay according to the official IELTS Writing assessment criteria and provide your response in the following JSON format:

{
    \"overall_band_score\": 7.0,
    \"criteria_scores\": {
        \"task_achievement\": {
            \"score\": 7.0,
            \"analysis\": \"Detailed analysis of how well the task requirements are met...\",
            \"strengths\": [\"List of specific strengths\"],
            \"weaknesses\": [\"List of specific areas for improvement\"],
            \"suggestions\": [\"Specific actionable suggestions\"]
        },
        \"coherence_cohesion\": {
            \"score\": 7.0,
            \"analysis\": \"Analysis of organization and linking...\",
            \"strengths\": [\"List of strengths\"],
            \"weaknesses\": [\"List of weaknesses\"],
            \"suggestions\": [\"Specific suggestions\"]
        },
        \"lexical_resource\": {
            \"score\": 7.0,
            \"analysis\": \"Analysis of vocabulary usage...\",
            \"strengths\": [\"List of strengths\"],
            \"weaknesses\": [\"List of weaknesses\"],
            \"suggestions\": [\"Specific suggestions\"]
        },
        \"grammatical_range\": {
            \"score\": 7.0,
            \"analysis\": \"Analysis of grammar usage...\",
            \"strengths\": [\"List of strengths\"],
            \"weaknesses\": [\"List of weaknesses\"],
            \"suggestions\": [\"Specific suggestions\"]
        }
    },
    \"detailed_feedback\": {
        \"introduction\": \"Analysis of introduction paragraph\",
        \"body_paragraphs\": [\"Analysis of each body paragraph\"],
        \"conclusion\": \"Analysis of conclusion\",
        \"overall_structure\": \"Overall structural analysis\"
    },
    \"improvement_priorities\": [
        \"Most important areas to focus on for improvement\"
    ],
    \"estimated_study_time\": \"Estimated time needed to reach next band level\",
    \"sample_improvements\": {
        \"vocabulary\": [\"Better word choices with examples\"],
        \"grammar\": [\"Grammar improvements with examples\"],
        \"structure\": [\"Structural improvements\"]
    }
}

{$taskInstructions}

Be extremely thorough, specific, and constructive in your feedback. Provide concrete examples from the text and specific suggestions for improvement.
";
        
        return $scoringPrompt;
    }
    
    /**
     * Get task-specific instructions
     */
    private function getTaskInstructions($taskType) {
        switch ($taskType) {
            case 'task1_academic':
                return "
TASK 1 ACADEMIC SPECIFIC CRITERIA:
- Minimum 150 words
- Describe visual information accurately
- Identify key trends and features
- Make relevant comparisons
- Use appropriate academic vocabulary
- Avoid personal opinions
";
            case 'task1_general':
                return "
TASK 1 GENERAL TRAINING SPECIFIC CRITERIA:
- Minimum 150 words
- Address all bullet points in the prompt
- Use appropriate tone (formal/informal/semi-formal)
- Follow letter format conventions
- Express purpose clearly
";
            case 'task2':
                return "
TASK 2 SPECIFIC CRITERIA:
- Minimum 250 words
- Present clear position on the topic
- Support arguments with examples
- Address all parts of the question
- Demonstrate critical thinking
- Use formal academic style
";
            default:
                return "";
        }
    }
    
    /**
     * Call OpenAI API
     */
    private function callOpenAI($prompt) {
        $data = [
            'model' => $this->model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => MAX_TOKENS,
            'temperature' => TEMPERATURE,
            'top_p' => TOP_P,
            'presence_penalty' => PRESENCE_PENALTY,
            'frequency_penalty' => FREQUENCY_PENALTY
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            throw new Exception('cURL Error: ' . curl_error($ch));
        }
        
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('API Error: HTTP ' . $httpCode . ' - ' . $response);
        }
        
        $decodedResponse = json_decode($response, true);
        
        if (!$decodedResponse || !isset($decodedResponse['choices'][0]['message']['content'])) {
            throw new Exception('Invalid API response format');
        }
        
        return $decodedResponse['choices'][0]['message']['content'];
    }
    
    /**
     * Parse OpenAI response and structure the data
     */
    private function parseResponse($response) {
        // Try to extract JSON from the response
        $jsonStart = strpos($response, '{');
        $jsonEnd = strrpos($response, '}');
        
        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonString = substr($response, $jsonStart, $jsonEnd - $jsonStart + 1);
            $parsed = json_decode($jsonString, true);
            
            if ($parsed) {
                return $parsed;
            }
        }
        
        // Fallback: return raw response if JSON parsing fails
        return [
            'error' => false,
            'raw_response' => $response,
            'overall_band_score' => 0,
            'message' => 'Response received but could not parse structured data'
        ];
    }
    
    /**
     * Get band score description
     */
    public function getBandDescription($score) {
        $bandScores = BAND_SCORES;
        
        // Find the closest band score
        $closestScore = 1.0;
        foreach (array_keys($bandScores) as $band) {
            if ($score >= $band) {
                $closestScore = $band;
                break;
            }
        }
        
        return $bandScores[$closestScore];
    }
    
    /**
     * Calculate overall band score from criteria scores
     */
    public function calculateOverallScore($criteriaScores) {
        $total = 0;
        $count = 0;
        
        foreach (SCORING_CRITERIA as $criterion => $config) {
            if (isset($criteriaScores[$criterion]['score'])) {
                $total += $criteriaScores[$criterion]['score'] * $config['weight'];
                $count++;
            }
        }
        
        if ($count === 0) return 0;
        
        $average = $total / ($count * 0.25); // Since all weights are 0.25
        
        // Round to nearest 0.5
        return round($average * 2) / 2;
    }
}
?>
