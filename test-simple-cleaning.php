<?php
// Simple test for data cleaning without reflection

function cleanTextContent($text) {
    if (!$text) return '';
    
    // Remove HTML tags
    $text = strip_tags($text);
    
    // Decode HTML entities
    $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    // Remove data attributes that might have leaked through
    $text = preg_replace('/data-[a-zA-Z-]+=["\'"][^"\']*["\'"]/', '', $text);

    // Remove title attributes
    $text = preg_replace('/title=["\'"][^"\']*["\'"]/', '', $text);
    
    // Clean up extra whitespace
    $text = preg_replace('/\s+/', ' ', $text);
    $text = trim($text);
    
    return $text;
}

echo "<h1>Simple Data Cleaning Test</h1>";

$dirtyTexts = [
    'data-correction-id="correction-0" data-original="people believes" title="Click">people believes',
    '<span class="highlight">some text</span>',
    'Normal text without issues',
    'Text with &quot;quotes&quot; and &amp; symbols',
    'data-type="grammar" data-severity="high">education is important'
];

echo "<h3>Text Cleaning Results:</h3>";
foreach ($dirtyTexts as $i => $dirtyText) {
    $cleanText = cleanTextContent($dirtyText);
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd;'>";
    echo "<strong>Test " . ($i + 1) . ":</strong><br>";
    echo "<strong>Dirty:</strong> " . htmlspecialchars($dirtyText) . "<br>";
    echo "<strong>Clean:</strong> " . htmlspecialchars($cleanText) . "<br>";
    echo "</div>";
}

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h3 { color: #333; }
</style>";
?>
