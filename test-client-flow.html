<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Flow Test - IELTS Scorer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/essay-highlighter.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; padding: 20px; }
        .container { max-width: 1200px; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #dee2e6; border-radius: 10px; }
        .log-area { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; max-height: 200px; overflow-y: auto; }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-cogs"></i> Client Flow Test</h1>
        <p class="lead">Test the complete client-side flow with ultra cleaning</p>

        <!-- Test Form -->
        <div class="test-section">
            <h3><i class="fas fa-play"></i> Test Client Flow</h3>
            <form id="testForm">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="taskType" class="form-label">Task Type</label>
                        <select class="form-select" id="taskType" required>
                            <option value="task2">Task 2 (Essay)</option>
                            <option value="task1_academic">Task 1 Academic</option>
                            <option value="task1_general">Task 1 General</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="prompt" class="form-label">Prompt</label>
                        <input type="text" class="form-control" id="prompt" 
                               value="Discuss the importance of education in modern society.">
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="essay" class="form-label">Test Essay</label>
                    <textarea class="form-control" id="essay" rows="8" required>Many people believes that education is important than ever in modern society. Students should only learn knowledge from books to achieve success. Education helps people to develop their skills and knowledge which are essential for personal growth.</textarea>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-rocket"></i> Test Complete Flow
                </button>
            </form>
        </div>

        <!-- Status Log -->
        <div class="test-section">
            <h3><i class="fas fa-list"></i> Process Log</h3>
            <div id="logArea" class="log-area">
                Ready to test...
            </div>
        </div>

        <!-- Results Display -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> Results</h3>
            
            <!-- Overall Score -->
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">Overall Score</h5>
                            <div class="h2 text-primary" id="overallScore">-</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">Total Corrections</h5>
                            <div class="h2 text-info" id="totalCorrections">-</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">Clean Corrections</h5>
                            <div class="h2 text-success" id="cleanCorrections">-</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">Success Rate</h5>
                            <div class="h2 text-warning" id="successRate">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Essay Display -->
            <div class="row">
                <div class="col-lg-8">
                    <h5><i class="fas fa-highlighter"></i> Essay with Highlighting</h5>
                    <div id="highlightedEssayContainer" style="min-height: 200px; background: #f8f9fa; padding: 20px; border-radius: 10px;">
                        Essay will appear here after processing...
                    </div>
                </div>
                <div class="col-lg-4">
                    <h5><i class="fas fa-list"></i> Corrections List</h5>
                    <div id="correctionsList" style="max-height: 400px; overflow-y: auto;">
                        Corrections will appear here...
                    </div>
                </div>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="test-section">
            <h3><i class="fas fa-bug"></i> Debug Information</h3>
            <div id="debugInfo" class="log-area">
                Debug information will appear here...
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/essay-highlighter.js"></script>
    <script src="assets/progress-tracker.js"></script>

    <script>
        let testLog = [];
        let testClient = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push(logEntry);
            
            const logArea = document.getElementById('logArea');
            const debugInfo = document.getElementById('debugInfo');
            
            if (logArea) {
                logArea.innerHTML = testLog.join('\n');
                logArea.scrollTop = logArea.scrollHeight;
            }
            
            if (debugInfo) {
                debugInfo.innerHTML = testLog.join('\n');
                debugInfo.scrollTop = debugInfo.scrollHeight;
            }
            
            console.log(logEntry);
        }

        // Custom IELTS Client for testing
        class TestIELTSClient {
            constructor() {
                this.apiUrl = 'api.php';
                this.isProcessing = false;
                this.highlighter = null;
                this.lastResult = null;
                
                log('✅ Test client initialized');
                this.initializeComponents();
            }
            
            initializeComponents() {
                try {
                    this.highlighter = new EssayHighlighter('highlightedEssayContainer');
                    log('✅ Highlighter component loaded');
                } catch (error) {
                    log('❌ Failed to load highlighter: ' + error.message, 'error');
                }
            }
            
            async testCompleteFlow(formData) {
                log('🚀 Starting complete flow test...');
                
                try {
                    // Step 1: Validate form data
                    log('📝 Step 1: Validating form data...');
                    this.validateFormData(formData);
                    log('✅ Form data validation passed');
                    
                    // Step 2: Call API
                    log('🌐 Step 2: Calling API...');
                    const result = await this.callAPI(formData);
                    log('✅ API call successful');
                    
                    // Step 3: Validate API response
                    log('🔍 Step 3: Validating API response...');
                    this.validateApiResponse(result);
                    log('✅ API response validation passed');
                    
                    // Step 4: Clean and validate corrections
                    log('🧹 Step 4: Cleaning and validating corrections...');
                    const cleanCorrections = this.validateAndCleanCorrections(result.highlighted_corrections || []);
                    log(`✅ Cleaning completed: ${cleanCorrections.length} clean corrections`);
                    
                    // Step 5: Update UI
                    log('🎨 Step 5: Updating UI...');
                    this.updateUI(result, cleanCorrections, formData.essay);
                    log('✅ UI update completed');
                    
                    // Step 6: Test highlighting
                    log('🎯 Step 6: Testing highlighting...');
                    this.testHighlighting(result, cleanCorrections, formData.essay);
                    log('✅ Highlighting test completed');
                    
                    log('🎉 COMPLETE FLOW TEST SUCCESSFUL!');
                    return true;
                    
                } catch (error) {
                    log('❌ Flow test failed: ' + error.message, 'error');
                    this.displayError(error.message);
                    return false;
                }
            }
            
            validateFormData(data) {
                if (!data.essay.trim()) throw new Error('Essay is empty');
                if (!data.task_type) throw new Error('Task type not selected');
                if (data.essay.trim().split(/\s+/).length < 10) throw new Error('Essay too short');
            }
            
            async callAPI(data) {
                const response = await fetch(this.apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const result = await response.json();
                this.lastResult = result;
                
                if (result.error) {
                    throw new Error(result.message || 'API error');
                }
                
                return result;
            }
            
            validateApiResponse(result) {
                if (!result.overall_band_score) throw new Error('No band score in response');
                if (!Array.isArray(result.highlighted_corrections)) throw new Error('Invalid corrections format');
            }
            
            validateAndCleanCorrections(corrections) {
                const cleanCorrections = [];
                
                for (const correction of corrections) {
                    const originalText = correction.original_text || '';
                    
                    const isClean = (
                        originalText.length > 2 &&
                        originalText.indexOf('data-') === -1 &&
                        originalText.indexOf('<') === -1 &&
                        originalText.indexOf('>') === -1 &&
                        originalText.indexOf('=') === -1 &&
                        originalText.indexOf('correction-') === -1
                    );
                    
                    if (isClean) {
                        cleanCorrections.push(correction);
                        log(`✅ Clean: "${originalText}"`);
                    } else {
                        log(`❌ Dirty: "${originalText}"`);
                    }
                }
                
                return cleanCorrections;
            }
            
            updateUI(result, cleanCorrections, originalEssay) {
                // Update metrics
                document.getElementById('overallScore').textContent = result.overall_band_score || 'N/A';
                document.getElementById('totalCorrections').textContent = (result.highlighted_corrections || []).length;
                document.getElementById('cleanCorrections').textContent = cleanCorrections.length;
                
                const successRate = cleanCorrections.length > 0 ? 
                    Math.round((cleanCorrections.length / (result.highlighted_corrections || []).length) * 100) : 0;
                document.getElementById('successRate').textContent = successRate + '%';
                
                // Update corrections list
                const correctionsList = document.getElementById('correctionsList');
                if (cleanCorrections.length > 0) {
                    correctionsList.innerHTML = cleanCorrections.map((correction, index) => `
                        <div class="card mb-2">
                            <div class="card-body">
                                <h6>Correction ${index + 1}</h6>
                                <p><strong>Original:</strong> "${correction.original_text}"</p>
                                <p><strong>Suggested:</strong> "${correction.suggested_correction}"</p>
                                <p><strong>Type:</strong> ${correction.error_type}</p>
                            </div>
                        </div>
                    `).join('');
                } else {
                    correctionsList.innerHTML = '<p class="text-muted">No clean corrections found</p>';
                }
            }
            
            testHighlighting(result, cleanCorrections, originalEssay) {
                try {
                    if (this.highlighter && cleanCorrections.length > 0) {
                        const cleanResult = { ...result, highlighted_corrections: cleanCorrections };
                        this.highlighter.init(cleanResult, originalEssay);
                        log('✅ Highlighting successful');
                    } else {
                        // Show essay without highlighting
                        const container = document.getElementById('highlightedEssayContainer');
                        container.innerHTML = `
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-info-circle"></i> 
                                ${cleanCorrections.length === 0 ? 'No clean corrections for highlighting' : 'Highlighting not available'}
                            </div>
                            <div style="padding: 15px; background: white; border-radius: 5px;">
                                ${originalEssay.replace(/\n/g, '<br>')}
                            </div>
                        `;
                        log('⚠️ Showing essay without highlighting');
                    }
                } catch (error) {
                    log('❌ Highlighting failed: ' + error.message);
                    throw error;
                }
            }
            
            displayError(message) {
                const container = document.getElementById('highlightedEssayContainer');
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Error:</strong> ${message}
                    </div>
                `;
            }
        }

        // Initialize test client
        document.addEventListener('DOMContentLoaded', function() {
            testClient = new TestIELTSClient();
            
            // Bind form submission
            document.getElementById('testForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = {
                    essay: document.getElementById('essay').value,
                    task_type: document.getElementById('taskType').value,
                    prompt: document.getElementById('prompt').value
                };
                
                await testClient.testCompleteFlow(formData);
            });
        });
    </script>
</body>
</html>
