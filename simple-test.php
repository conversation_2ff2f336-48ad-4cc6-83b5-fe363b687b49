<?php
// Simple test to check if everything works

echo "<!DOCTYPE html>
<html>
<head>
    <title>Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>";

echo "<h1>🧪 Simple System Test</h1>";

// Test 1: Check if files exist
echo "<h3>1. File Check</h3>";
$files = ['config.php', 'IELTSScorer.php'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ {$file} exists</div>";
    } else {
        echo "<div class='error'>❌ {$file} missing</div>";
    }
}

// Test 2: Include files
echo "<h3>2. Include Test</h3>";
try {
    require_once 'config.php';
    echo "<div class='success'>✅ config.php included</div>";
    
    require_once 'IELTSScorer.php';
    echo "<div class='success'>✅ IELTSScorer.php included</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ Include error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Test 3: Check constants
echo "<h3>3. Configuration Check</h3>";
$constants = ['OPENAI_API_URL', 'OPENAI_API_KEY', 'OPENAI_MODEL'];
foreach ($constants as $const) {
    if (defined($const)) {
        echo "<div class='success'>✅ {$const} defined</div>";
    } else {
        echo "<div class='error'>❌ {$const} not defined</div>";
    }
}

// Test 4: Create scorer instance
echo "<h3>4. Scorer Instance Test</h3>";
try {
    $scorer = new IELTSScorer();
    echo "<div class='success'>✅ IELTSScorer instance created</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ Scorer creation failed: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Test 5: Simple API test
echo "<h3>5. Simple API Test</h3>";
try {
    $testEssay = "Education is important.";
    $result = $scorer->scoreEssay($testEssay, 'task2', 'Discuss education.');
    
    if (isset($result['error']) && $result['error']) {
        echo "<div class='error'>❌ API Error: " . htmlspecialchars($result['message']) . "</div>";
    } else {
        echo "<div class='success'>✅ API call successful</div>";
        echo "<div class='info'>Band Score: " . ($result['overall_band_score'] ?? 'N/A') . "</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ API Test failed: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</body></html>";
?>
