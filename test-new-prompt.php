<?php
require_once 'config.php';
require_once 'IELTSScorer.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>New Prompt Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .correction { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
    </style>
</head>
<body>";

echo "<h1>New Prompt Test - Clean Response Check</h1>";

$testEssay = "Many people believes that education is important than ever. Students should only learn knowledge from books to do well in exam.";
$taskType = 'task2';
$prompt = 'Education is important. Discuss the role of books in learning.';

try {
    echo "<div class='success'>Testing with essay: " . htmlspecialchars($testEssay) . "</div>";
    
    $scorer = new IELTSScorer();
    $result = $scorer->scoreEssay($testEssay, $taskType, $prompt);
    
    if (isset($result['error']) && $result['error']) {
        echo "<div class='error'>Error: " . htmlspecialchars($result['message']) . "</div>";
    } else {
        echo "<div class='success'>✅ Scoring successful! Overall score: " . ($result['overall_band_score'] ?? 'N/A') . "</div>";
        
        if (isset($result['highlighted_corrections']) && is_array($result['highlighted_corrections'])) {
            echo "<h3>Corrections Analysis (" . count($result['highlighted_corrections']) . " found):</h3>";
            
            $allClean = true;
            foreach ($result['highlighted_corrections'] as $i => $correction) {
                echo "<div class='correction'>";
                echo "<h4>Correction " . ($i + 1) . "</h4>";
                
                $originalText = $correction['original_text'] ?? '';
                $suggestedText = $correction['suggested_correction'] ?? '';
                $explanation = $correction['explanation'] ?? '';
                
                echo "<strong>Original:</strong> \"" . htmlspecialchars($originalText) . "\"<br>";
                echo "<strong>Suggested:</strong> \"" . htmlspecialchars($suggestedText) . "\"<br>";
                echo "<strong>Type:</strong> " . htmlspecialchars($correction['error_type'] ?? 'N/A') . "<br>";
                echo "<strong>Severity:</strong> " . htmlspecialchars($correction['severity'] ?? 'N/A') . "<br>";
                echo "<strong>Explanation:</strong> " . htmlspecialchars($explanation) . "<br>";
                
                // Check for HTML artifacts in each field
                $hasHtmlInOriginal = (strpos($originalText, '<') !== false || strpos($originalText, 'data-') !== false);
                $hasHtmlInSuggested = (strpos($suggestedText, '<') !== false || strpos($suggestedText, 'data-') !== false);
                $hasHtmlInExplanation = (strpos($explanation, '<') !== false || strpos($explanation, 'data-') !== false);
                
                if ($hasHtmlInOriginal || $hasHtmlInSuggested || $hasHtmlInExplanation) {
                    echo "<div style='color: red; font-weight: bold;'>❌ HTML artifacts detected!</div>";
                    $allClean = false;
                    
                    if ($hasHtmlInOriginal) echo "<div style='color: red;'>- Original text has HTML</div>";
                    if ($hasHtmlInSuggested) echo "<div style='color: red;'>- Suggested text has HTML</div>";
                    if ($hasHtmlInExplanation) echo "<div style='color: red;'>- Explanation has HTML</div>";
                } else {
                    echo "<div style='color: green; font-weight: bold;'>✅ Clean text - no HTML artifacts</div>";
                }
                echo "</div>";
            }
            
            // Overall status
            if ($allClean) {
                echo "<div class='success'><h3>🎉 SUCCESS: All corrections are clean text!</h3></div>";
            } else {
                echo "<div class='error'><h3>❌ FAILED: Some corrections still contain HTML artifacts</h3></div>";
            }
            
        } else {
            echo "<div class='error'>No highlighted_corrections found in response</div>";
        }
    }
    
    echo "<details style='margin-top: 20px;'>";
    echo "<summary>Raw API Response (for debugging)</summary>";
    echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>";
    echo "</details>";
    
} catch (Exception $e) {
    echo "<div class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</body></html>";
?>
