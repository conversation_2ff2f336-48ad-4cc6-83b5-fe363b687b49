# IELTS Writing Scorer

Hệ thống chấm điểm viết IELTS chuyên nghiệp sử dụng AI (OpenAI GPT-4o-mini) để đánh giá và phân tích bài viết IELTS một cách chi tiết và chính xác.

## Tính năng chính

### 🎯 Chấm điểm toàn diện
- **4 tiêu chí chấm điểm IELTS chính thức:**
  - Task Achievement/Response (25%)
  - Coherence and Cohesion (25%) 
  - Lexical Resource (25%)
  - Grammatical Range and Accuracy (25%)

### 📊 Phân tích chi tiết
- Điểm số tổng thể (Band Score 1.0 - 9.0)
- <PERSON><PERSON><PERSON>m số từng tiêu chí với phân tích cụ thể
- <PERSON>ân tích điểm mạnh và điểm yếu
- Gợi ý cải thiện cụ thể và khả thi

### 📝 Hỗ trợ đa dạng loại bài
- **Task 1 Academic:** <PERSON><PERSON> tả biểu đồ, b<PERSON><PERSON>, s<PERSON> đồ
- **Task 1 General Training:** Vi<PERSON><PERSON> thư
- **Task 2:** Bài luận argumentative/discussion

### 🔧 Tính năng hỗ trợ
- Đếm từ tự động với mã màu theo yêu cầu
- Tự động lưu bản nháp
- Giao diện thân thiện, responsive
- Hiển thị thời gian ước tính để cải thiện

## Cài đặt

### Yêu cầu hệ thống
- PHP 7.4 hoặc cao hơn
- cURL extension
- JSON extension
- OpenSSL extension
- Web server (Apache/Nginx)

### Hướng dẫn cài đặt

1. **Clone hoặc tải về source code**
```bash
git clone [repository-url]
cd ielts-writing-scorer
```

2. **Cấu hình API Key**
Mở file `config.php` và cập nhật API key:
```php
define('OPENAI_API_KEY', 'your-api-key-here');
```

3. **Cấu hình web server**
- Đặt thư mục dự án vào document root
- Đảm bảo PHP có quyền đọc/ghi file

4. **Kiểm tra cài đặt**
Truy cập `test.php` để kiểm tra:
- Kết nối API
- Cấu hình hệ thống
- Test chấm điểm mẫu

## Sử dụng

### Giao diện web
1. Truy cập `index.php`
2. Chọn loại task (Task 1 Academic/General, Task 2)
3. Nhập prompt đề bài (tùy chọn)
4. Dán bài viết cần chấm điểm
5. Nhấn "Score My Essay"

### Sử dụng programmatically
```php
require_once 'IELTSScorer.php';

$scorer = new IELTSScorer();
$result = $scorer->scoreEssay($essay, $taskType, $prompt);

// Xử lý kết quả
if (!isset($result['error'])) {
    echo "Overall Score: " . $result['overall_band_score'];
    // Xử lý các tiêu chí khác...
}
```

## Cấu trúc dự án

```
ielts-writing-scorer/
├── config.php              # Cấu hình chính
├── IELTSScorer.php         # Class chấm điểm chính
├── index.php               # Giao diện web
├── examples.php            # Bài viết mẫu
├── test.php               # Script kiểm tra
└── README.md              # Tài liệu này
```

## API Response Format

```json
{
    "overall_band_score": 7.0,
    "criteria_scores": {
        "task_achievement": {
            "score": 7.0,
            "analysis": "Phân tích chi tiết...",
            "strengths": ["Điểm mạnh 1", "Điểm mạnh 2"],
            "weaknesses": ["Điểm yếu 1", "Điểm yếu 2"],
            "suggestions": ["Gợi ý 1", "Gợi ý 2"]
        },
        // ... các tiêu chí khác
    },
    "detailed_feedback": {
        "introduction": "Phân tích đoạn mở bài",
        "body_paragraphs": ["Phân tích từng đoạn thân bài"],
        "conclusion": "Phân tích đoạn kết luận",
        "overall_structure": "Phân tích cấu trúc tổng thể"
    },
    "improvement_priorities": [
        "Ưu tiên cải thiện 1",
        "Ưu tiên cải thiện 2"
    ],
    "sample_improvements": {
        "vocabulary": ["Cải thiện từ vựng"],
        "grammar": ["Cải thiện ngữ pháp"],
        "structure": ["Cải thiện cấu trúc"]
    },
    "metadata": {
        "task_type": "task2",
        "word_count": 287,
        "scored_at": "2024-01-15 10:30:00"
    }
}
```

## Tùy chỉnh

### Thay đổi model AI
Trong `config.php`:
```php
define('OPENAI_MODEL', 'gpt-4'); // Hoặc model khác
```

### Điều chỉnh tham số chấm điểm
```php
define('TEMPERATURE', 0.3);     // Độ nhất quán (0.0-1.0)
define('MAX_TOKENS', 4000);     // Độ dài response tối đa
```

### Thêm tiêu chí chấm điểm mới
Cập nhật `SCORING_CRITERIA` trong `config.php` và điều chỉnh prompt trong `IELTSScorer.php`.

## Troubleshooting

### Lỗi API Connection
- Kiểm tra API key có đúng không
- Kiểm tra kết nối internet
- Kiểm tra cURL extension đã được cài đặt

### Lỗi JSON Parsing
- API response có thể không đúng định dạng
- Kiểm tra `raw_response` trong kết quả lỗi
- Điều chỉnh prompt để có response nhất quán hơn

### Điểm số không chính xác
- Điều chỉnh `TEMPERATURE` thấp hơn (0.1-0.3)
- Cải thiện prompt với ví dụ cụ thể hơn
- Sử dụng model mạnh hơn (GPT-4)

## Bảo mật

- **Không commit API key** vào version control
- Sử dụng environment variables cho production
- Implement rate limiting cho API calls
- Validate và sanitize user input

## Đóng góp

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request

## License

MIT License - xem file LICENSE để biết chi tiết.

## Liên hệ

- Email: [your-email]
- GitHub: [your-github]

---

**Lưu ý:** Đây là công cụ hỗ trợ học tập. Kết quả chấm điểm chỉ mang tính tham khảo và không thể thay thế hoàn toàn việc đánh giá của giám khảo IELTS chính thức.
