<?php
require_once 'config.php';
require_once 'IELTSScorer.php';

// Handle form submission
$result = null;
$error = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $essay = $_POST['essay'] ?? '';
        $taskType = $_POST['task_type'] ?? '';
        $prompt = $_POST['prompt'] ?? '';

        if (empty($essay)) {
            throw new Exception('Please enter your essay text');
        }

        if (empty($taskType)) {
            throw new Exception('Please select a task type');
        }

        // Enhanced error handling for scoring
        $scorer = new IELTSScorer();
        $result = $scorer->scoreEssay($essay, $taskType, $prompt);

        // Check if the result contains an error
        if (isset($result['error']) && $result['error']) {
            $error = $result['message'] ?? 'An unknown error occurred during scoring';
            // Keep the result for debugging
            $result['debug_info'] = [
                'essay_length' => strlen($essay),
                'task_type' => $taskType,
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }

    } catch (Exception $e) {
        $error = 'System Error: ' . $e->getMessage();
        $result = [
            'error' => true,
            'message' => $e->getMessage(),
            'debug_info' => [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/essay-highlighter.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .header h1 {
            color: #667eea;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .result-section {
            background: #fff;
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .score-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .score-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .criteria-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .criteria-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .criteria-score {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .feedback-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .word-count {
            font-size: 0.9rem;
            color: #6c757d;
            text-align: right;
            margin-top: 5px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner-border {
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-container">
            <div class="header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="fas fa-graduation-cap"></i> <?php echo APP_NAME; ?></h1>
                        <p class="lead">Comprehensive AI-powered IELTS Writing Assessment</p>
                        <small class="text-muted">Version <?php echo APP_VERSION; ?></small>
                    </div>
                    <div>
                        <a href="dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-chart-line"></i> View Progress
                        </a>
                    </div>
                </div>
            </div>

            <!-- Input Form -->
            <div class="form-section">
                <form method="POST" id="scoringForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="task_type" class="form-label">
                                <i class="fas fa-tasks"></i> Task Type
                            </label>
                            <select class="form-select" id="task_type" name="task_type" required>
                                <option value="">Select Task Type</option>
                                <?php foreach (TASK_TYPES as $key => $label): ?>
                                    <option value="<?php echo $key; ?>"
                                            <?php echo (isset($_POST['task_type']) && $_POST['task_type'] === $key) ? 'selected' : ''; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="prompt" class="form-label">
                                <i class="fas fa-question-circle"></i> Task Prompt (Optional)
                            </label>
                            <input type="text" class="form-control" id="prompt" name="prompt"
                                   placeholder="Enter the original task prompt..."
                                   value="<?php echo htmlspecialchars($_POST['prompt'] ?? ''); ?>">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="essay" class="form-label">
                            <i class="fas fa-edit"></i> Your Essay
                        </label>
                        <textarea class="form-control" id="essay" name="essay" rows="15"
                                  placeholder="Paste your IELTS writing response here..."
                                  required><?php echo htmlspecialchars($_POST['essay'] ?? ''); ?></textarea>
                        <div class="word-count" id="wordCount">Word count: 0</div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-chart-line"></i> Score My Essay
                        </button>
                    </div>
                </form>

                <div class="loading" id="loading">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Analyzing your essay... This may take a few moments.</p>
                </div>
            </div>

            <!-- Error Display -->
            <?php if ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            <!-- Results Display -->
            <?php if ($result && !isset($result['error'])): ?>
                <div class="result-section">
                    <h2><i class="fas fa-chart-bar"></i> Scoring Results</h2>

                    <!-- Overall Score -->
                    <div class="score-card">
                        <div class="score-number"><?php echo $result['overall_band_score'] ?? 'N/A'; ?></div>
                        <h4>Overall Band Score</h4>
                        <p><?php
                            if (isset($result['overall_band_score'])) {
                                $scorer = new IELTSScorer();
                                echo $scorer->getBandDescription($result['overall_band_score']);
                            }
                        ?></p>
                    </div>

                    <!-- Criteria Scores -->
                    <?php if (isset($result['criteria_scores'])): ?>
                        <div class="row">
                            <?php foreach ($result['criteria_scores'] as $criterion => $data): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="criteria-card">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h5><?php echo SCORING_CRITERIA[$criterion]['name'] ?? ucfirst(str_replace('_', ' ', $criterion)); ?></h5>
                                            <span class="criteria-score"><?php echo $data['score'] ?? 'N/A'; ?></span>
                                        </div>
                                        <p class="text-muted small"><?php echo $data['analysis'] ?? ''; ?></p>

                                        <?php if (!empty($data['strengths'])): ?>
                                            <div class="mb-2">
                                                <strong class="text-success">Strengths:</strong>
                                                <ul class="small">
                                                    <?php foreach ($data['strengths'] as $strength): ?>
                                                        <li><?php echo htmlspecialchars($strength); ?></li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (!empty($data['suggestions'])): ?>
                                            <div>
                                                <strong class="text-primary">Suggestions:</strong>
                                                <ul class="small">
                                                    <?php foreach ($data['suggestions'] as $suggestion): ?>
                                                        <li><?php echo htmlspecialchars($suggestion); ?></li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Detailed Feedback -->
                    <?php if (isset($result['detailed_feedback'])): ?>
                        <div class="feedback-section">
                            <h4><i class="fas fa-comments"></i> Detailed Feedback</h4>
                            <?php foreach ($result['detailed_feedback'] as $section => $feedback): ?>
                                <div class="mb-3">
                                    <h6><?php echo ucfirst(str_replace('_', ' ', $section)); ?>:</h6>
                                    <?php if (is_array($feedback)): ?>
                                        <ul>
                                            <?php foreach ($feedback as $item): ?>
                                                <li><?php echo htmlspecialchars($item); ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php else: ?>
                                        <p><?php echo htmlspecialchars($feedback); ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Improvement Priorities -->
                    <?php if (isset($result['improvement_priorities'])): ?>
                        <div class="feedback-section">
                            <h4><i class="fas fa-target"></i> Priority Areas for Improvement</h4>
                            <ul>
                                <?php foreach ($result['improvement_priorities'] as $priority): ?>
                                    <li><?php echo htmlspecialchars($priority); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <!-- Sample Improvements -->
                    <?php if (isset($result['sample_improvements'])): ?>
                        <div class="feedback-section">
                            <h4><i class="fas fa-lightbulb"></i> Sample Improvements</h4>
                            <?php foreach ($result['sample_improvements'] as $category => $improvements): ?>
                                <div class="mb-3">
                                    <h6><?php echo ucfirst($category); ?>:</h6>
                                    <ul>
                                        <?php foreach ($improvements as $improvement): ?>
                                            <li><?php echo htmlspecialchars($improvement); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Highlighted Essay with Corrections -->
                    <div id="highlightedEssayContainer"></div>

                    <!-- Metadata -->
                    <?php if (isset($result['metadata'])): ?>
                        <div class="mt-4">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Word count: <?php echo $result['metadata']['word_count']; ?> |
                                Task type: <?php echo TASK_TYPES[$result['metadata']['task_type']] ?? $result['metadata']['task_type']; ?> |
                                Scored at: <?php echo $result['metadata']['scored_at']; ?>
                                <?php if (isset($result['estimated_study_time'])): ?>
                                    | Estimated study time: <?php echo $result['estimated_study_time']; ?>
                                <?php endif; ?>
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            <?php elseif ($result && isset($result['error'])): ?>
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Scoring Error:</strong> <?php echo htmlspecialchars($result['message'] ?? 'An error occurred during scoring'); ?>

                    <?php if (isset($result['debug_info'])): ?>
                        <details class="mt-3">
                            <summary><i class="fas fa-bug"></i> Debug Information</summary>
                            <div class="mt-2">
                                <?php if (isset($result['debug_info']['essay_length'])): ?>
                                    <strong>Essay Length:</strong> <?php echo $result['debug_info']['essay_length']; ?> characters<br>
                                <?php endif; ?>
                                <?php if (isset($result['debug_info']['task_type'])): ?>
                                    <strong>Task Type:</strong> <?php echo htmlspecialchars($result['debug_info']['task_type']); ?><br>
                                <?php endif; ?>
                                <?php if (isset($result['debug_info']['timestamp'])): ?>
                                    <strong>Timestamp:</strong> <?php echo $result['debug_info']['timestamp']; ?><br>
                                <?php endif; ?>
                                <?php if (isset($result['debug_info']['file'])): ?>
                                    <strong>Error File:</strong> <?php echo htmlspecialchars($result['debug_info']['file']); ?><br>
                                    <strong>Error Line:</strong> <?php echo $result['debug_info']['line']; ?><br>
                                <?php endif; ?>
                            </div>
                        </details>
                    <?php endif; ?>

                    <?php if (isset($result['raw_response'])): ?>
                        <details class="mt-2">
                            <summary><i class="fas fa-code"></i> Raw API Response</summary>
                            <pre class="mt-2"><?php echo htmlspecialchars($result['raw_response']); ?></pre>
                        </details>
                    <?php endif; ?>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            If this error persists, please check your internet connection and try again.
                            You can also try with a shorter essay or different task type.
                        </small>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/essay-highlighter.js"></script>
    <script src="assets/progress-tracker.js"></script>
    <script>
        // Word count functionality
        const essayTextarea = document.getElementById('essay');
        const wordCountDiv = document.getElementById('wordCount');

        function updateWordCount() {
            const text = essayTextarea.value.trim();
            const wordCount = text === '' ? 0 : text.split(/\s+/).length;
            wordCountDiv.textContent = `Word count: ${wordCount}`;

            // Color coding based on task requirements
            const taskType = document.getElementById('task_type').value;
            const minWords = (taskType === 'task2') ? 250 : 150;

            if (wordCount < minWords) {
                wordCountDiv.style.color = '#dc3545'; // Red
            } else if (wordCount < minWords + 50) {
                wordCountDiv.style.color = '#ffc107'; // Yellow
            } else {
                wordCountDiv.style.color = '#28a745'; // Green
            }
        }

        essayTextarea.addEventListener('input', updateWordCount);
        document.getElementById('task_type').addEventListener('change', updateWordCount);

        // Form submission with loading
        document.getElementById('scoringForm').addEventListener('submit', function() {
            document.getElementById('loading').style.display = 'block';
            this.style.display = 'none';
        });

        // Initialize word count
        updateWordCount();

        // Auto-save functionality (optional)
        function autoSave() {
            const essay = essayTextarea.value;
            const taskType = document.getElementById('task_type').value;
            const prompt = document.getElementById('prompt').value;

            localStorage.setItem('ielts_essay_draft', JSON.stringify({
                essay: essay,
                task_type: taskType,
                prompt: prompt,
                saved_at: new Date().toISOString()
            }));
        }

        // Load saved draft
        function loadDraft() {
            const saved = localStorage.getItem('ielts_essay_draft');
            if (saved) {
                try {
                    const data = JSON.parse(saved);
                    if (data.essay && !essayTextarea.value) {
                        if (confirm('Found a saved draft. Would you like to load it?')) {
                            essayTextarea.value = data.essay;
                            document.getElementById('task_type').value = data.task_type || '';
                            document.getElementById('prompt').value = data.prompt || '';
                            updateWordCount();
                        }
                    }
                } catch (e) {
                    console.log('Error loading draft:', e);
                }
            }
        }

        // Auto-save every 30 seconds
        setInterval(autoSave, 30000);
        essayTextarea.addEventListener('input', autoSave);

        // Load draft on page load
        loadDraft();

        // Initialize essay highlighter and progress tracking if we have results
        <?php if ($result && !isset($result['error'])): ?>
        document.addEventListener('DOMContentLoaded', function() {
            const highlighter = new EssayHighlighter('highlightedEssayContainer');
            const progressTracker = new IELTSProgressTracker();
            const scoringResult = <?php echo json_encode($result); ?>;
            const originalEssay = <?php echo json_encode($_POST['essay'] ?? ''); ?>;
            const taskType = <?php echo json_encode($_POST['task_type'] ?? ''); ?>;

            if (scoringResult && originalEssay) {
                // Validate and clean corrections before highlighting
                console.log('Raw scoring result:', scoringResult);

                let cleanCorrections = 0;
                if (scoringResult.highlighted_corrections) {
                    cleanCorrections = scoringResult.highlighted_corrections.filter(correction => {
                        return correction.original_text &&
                               correction.original_text.indexOf('data-') === -1 &&
                               correction.original_text.indexOf('<') === -1 &&
                               correction.original_text.trim().length > 0;
                    }).length;
                }

                console.log(`Found ${cleanCorrections} clean corrections out of ${scoringResult.highlighted_corrections?.length || 0} total`);

                // Initialize highlighter with error handling
                try {
                    if (cleanCorrections > 0) {
                        highlighter.init(scoringResult, originalEssay);
                        console.log('Highlighter initialized successfully with clean data');
                    } else {
                        throw new Error('No clean corrections available for highlighting');
                    }
                } catch (error) {
                    console.error('Highlighter initialization failed:', error);
                    document.getElementById('highlightedEssayContainer').innerHTML =
                        '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> Highlighting temporarily unavailable due to data processing issues. Showing essay without highlights.</div>' +
                        '<div class="essay-content" style="padding: 20px; background: #f8f9fa; border-radius: 10px; margin-top: 15px;">' +
                        originalEssay.replace(/\n/g, '<br>') + '</div>' +
                        '<div class="alert alert-info mt-3"><i class="fas fa-info-circle"></i> Your essay has been scored successfully. The highlighting feature will be restored in the next update.</div>';
                }

                // Track progress
                progressTracker.addSession(scoringResult, originalEssay, taskType);

                // Show success message with progress info
                const summary = progressTracker.getProgressSummary();
                if (summary.totalEssays > 1) {
                    const progressAlert = document.createElement('div');
                    progressAlert.className = 'alert alert-info mt-3';
                    progressAlert.innerHTML = `
                        <i class="fas fa-chart-line"></i>
                        <strong>Progress Update:</strong> This is your ${summary.totalEssays}th essay.
                        Your average score is ${summary.averageScore.toFixed(1)}.
                        <a href="dashboard.php" class="alert-link">View detailed progress</a>
                    `;
                    document.querySelector('.result-section').appendChild(progressAlert);
                }
            }
        });
        <?php endif; ?>
    </script>
</body>
</html>