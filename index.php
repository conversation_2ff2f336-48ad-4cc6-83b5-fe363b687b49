<?php
// IELTS Writing Scorer - Main Application
// Professional IELTS scoring system with interactive features

// Configuration
$config = [
    'app_name' => 'IELTS Writing Scorer',
    'version' => '2.0',
    'api_endpoint' => 'api.php',
    'debug_mode' => true
];

// Enhanced demo data with detailed criteria analysis
$demoData = [
    'overall_band_score' => 4.5,
    'criteria_scores' => [
        'task_achievement' => 4.0,
        'coherence_cohesion' => 4.5,
        'lexical_resource' => 4.5,
        'grammar_accuracy' => 4.0
    ],
    'detailed_feedback' => [
        'task_achievement' => [
            'score' => 4.0,
            'feedback' => 'Bài viết có đề cập đến cả hai quan điểm nhưng phân tích rất hời hợt và thiếu chiều sâu. Ý kiến cá nhân không được phát triển đầy đủ.',
            'issues' => [
                'Mở bài quá đơn giản, không paraphrase đề bài hiệu quả',
                'Thân bài 1 chỉ liệt kê lợi ích mà không phân tích sâu',
                'Thân bài 2 quá ngắn và thiếu lập luận thuyết phục',
                'Ý kiến cá nhân không rõ ràng và thiếu justification',
                'Kết bài quá vội vàng, không tóm tắt các ý chính'
            ],
            'improvements' => [
                'Viết mở bài với paraphrase chính xác và thesis statement rõ ràng',
                'Phát triển mỗi quan điểm với ít nhất 2-3 ý chính có giải thích',
                'Đưa ra ý kiến cá nhân mạnh mẽ với lý do cụ thể',
                'Bổ sung ví dụ thực tế để minh họa cho các luận điểm',
                'Viết kết bài tóm tắt và khẳng định lại position'
            ],
            'outline_suggestion' => [
                'Mở bài: Paraphrase đề bài + thesis statement (ủng hộ quan điểm nào)',
                'Thân bài 1: Lợi ích của việc học tin tức quốc tế (mở rộng kiến thức, cải thiện tiếng Anh)',
                'Thân bài 2: Nhược điểm của việc học tin tức quốc tế (mất thời gian, không thực tế)',
                'Thân bài 3: Ý kiến cá nhân + lý do (nên học nhưng có giới hạn)',
                'Kết bài: Tóm tắt + khẳng định lại quan điểm cá nhân'
            ]
        ],
        'coherence_cohesion' => [
            'score' => 4.5,
            'feedback' => 'Bài viết thiếu tổ chức logic và liên kết kém. Các đoạn văn không có cấu trúc rõ ràng và thiếu từ nối.',
            'issues' => [
                'Mở bài và kết bài quá đơn giản, thiếu professional',
                'Các đoạn văn không có topic sentence rõ ràng',
                'Thiếu từ nối giữa các câu và đoạn văn',
                'Sử dụng "Firstly, However" nhưng không có "Secondly, Finally" tương ứng',
                'Kết bài sử dụng "I conclusion" thay vì "In conclusion"'
            ],
            'improvements' => [
                'Sử dụng cấu trúc đoạn văn: Topic sentence + Supporting ideas + Examples',
                'Thêm từ nối: Moreover, Furthermore, In addition, On the other hand',
                'Sử dụng sequencing words: Firstly, Secondly, Finally một cách nhất quán',
                'Cải thiện mở bài và kết bài với academic phrases',
                'Sử dụng pronoun reference để tránh lặp từ'
            ]
        ],
        'lexical_resource' => [
            'score' => 4.5,
            'feedback' => 'Từ vựng hạn chế với nhiều lỗi chính tả nghiêm trọng. Thiếu từ vựng academic và có word choice không phù hợp.',
            'issues' => [
                'Lỗi chính tả cơ bản: "sometime" → "sometimes", "nessessary" → "necessary"',
                'Lỗi chính tả trong từ vựng phổ biến: "footbal" → "football"',
                'Từ vựng quá đơn giản và lặp lại nhiều lần',
                'Thiếu từ vựng academic và formal expressions',
                'Word choice không chính xác trong một số trường hợp'
            ],
            'improvements' => [
                'Kiểm tra chính tả cẩn thận, đặc biệt với từ vựng cơ bản',
                'Học từ vựng academic cho IELTS Writing Task 2',
                'Sử dụng synonyms để tránh lặp từ: students/pupils, important/significant',
                'Học collocations: "waste time", "gain knowledge", "develop skills"',
                'Sử dụng formal expressions thay vì informal language'
            ]
        ],
        'grammar_accuracy' => [
            'score' => 4.0,
            'feedback' => 'Có rất nhiều lỗi ngữ pháp cơ bản ảnh hưởng nghiêm trọng đến ý nghĩa. Cần cải thiện toàn diện về grammar.',
            'issues' => [
                'Lỗi subject-verb agreement nghiêm trọng: "it make", "student have"',
                'Sai cấu trúc với modal verbs: "should teaching" → "should teach"',
                'Thiếu be verb: "news not important" → "news is not important"',
                'Sai cấu trúc phủ định: "no need study" → "do not need to study"',
                'Lỗi plural/singular consistency trong toàn bài'
            ],
            'improvements' => [
                'Ôn lại subject-verb agreement: He/She/It + V-s/es',
                'Học cấu trúc modal verbs: should/can/must + V(base form)',
                'Luyện tập be verb trong câu khẳng định và phủ định',
                'Ôn cấu trúc need: need to + V(infinitive)',
                'Chú ý consistency về số ít/số nhiều trong cả bài'
            ]
        ]
    ],
    'highlighted_corrections' => [
        [
            'original_text' => 'sometime',
            'suggested_correction' => 'sometimes',
            'error_type' => 'spelling',
            'category' => 'lexical_resource',
            'explanation' => 'Lỗi chính tả: "sometime" (một lúc nào đó) → "sometimes" (thỉnh thoảng).',
            'severity' => 'medium',
            'spelling_rule' => 'Adverb of frequency'
        ],
        [
            'original_text' => 'nessessary',
            'suggested_correction' => 'necessary',
            'error_type' => 'spelling',
            'category' => 'lexical_resource',
            'explanation' => 'Lỗi chính tả nghiêm trọng: "nessessary" → "necessary". Từ này rất phổ biến trong IELTS.',
            'severity' => 'high',
            'spelling_rule' => 'Double consonants in adjectives'
        ],
        [
            'original_text' => 'student in secondary school need',
            'suggested_correction' => 'students in secondary school need',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => 'Lỗi số nhiều: "student" → "students" khi nói về học sinh nói chung.',
            'severity' => 'high',
            'grammar_rule' => 'Plural nouns for general statements'
        ],
        [
            'original_text' => 'other think this waste their time',
            'suggested_correction' => 'others think this wastes their time',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => '"Other" → "others" (đại từ). "This waste" → "this wastes" (subject-verb agreement).',
            'severity' => 'high',
            'grammar_rule' => 'Pronouns và subject-verb agreement'
        ],
        [
            'original_text' => 'both of these thing',
            'suggested_correction' => 'both of these things',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => '"Thing" → "things" (số nhiều sau "these").',
            'severity' => 'medium',
            'grammar_rule' => 'Plural nouns after demonstratives'
        ],
        [
            'original_text' => 'it make student smart',
            'suggested_correction' => 'it makes students smart',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => 'Subject-verb agreement: "it make" → "it makes". "Student" → "students".',
            'severity' => 'high',
            'grammar_rule' => 'Third person singular verb forms'
        ],
        [
            'original_text' => 'When student learn',
            'suggested_correction' => 'When students learn',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => '"Student" → "students" trong câu tổng quát.',
            'severity' => 'medium',
            'grammar_rule' => 'Plural nouns in general statements'
        ],
        [
            'original_text' => 'footbal match',
            'suggested_correction' => 'football matches',
            'error_type' => 'spelling',
            'category' => 'lexical_resource',
            'explanation' => 'Lỗi chính tả: "footbal" → "football". Và nên dùng số nhiều "matches".',
            'severity' => 'medium',
            'spelling_rule' => 'Common sports vocabulary'
        ],
        [
            'original_text' => 'They will very interesting',
            'suggested_correction' => 'They will be very interested',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => 'Thiếu "be" và sai adjective: "interesting" → "interested" (người cảm thấy thú vị).',
            'severity' => 'high',
            'grammar_rule' => 'Be verb và -ed/-ing adjectives'
        ],
        [
            'original_text' => 'is also help student speaking',
            'suggested_correction' => 'also helps students speak',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => 'Không dùng "is" với "help". "Help + object + infinitive without to".',
            'severity' => 'high',
            'grammar_rule' => 'Help + object + bare infinitive'
        ],
        [
            'original_text' => 'I thinking',
            'suggested_correction' => 'I think',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => 'Sai thì: "I thinking" → "I think" (simple present cho ý kiến).',
            'severity' => 'high',
            'grammar_rule' => 'Simple present for opinions'
        ],
        [
            'original_text' => 'Student have many important subject',
            'suggested_correction' => 'Students have many important subjects',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => '"Student" → "students", "subject" → "subjects" (số nhiều).',
            'severity' => 'medium',
            'grammar_rule' => 'Plural nouns consistency'
        ],
        [
            'original_text' => 'can help student become doctor or engineer',
            'suggested_correction' => 'can help students become doctors or engineers',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => 'Tất cả cần số nhiều: "student" → "students", "doctor" → "doctors", "engineer" → "engineers".',
            'severity' => 'medium',
            'grammar_rule' => 'Plural nouns for professions'
        ],
        [
            'original_text' => 'International news not really important',
            'suggested_correction' => 'International news is not really important',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => 'Thiếu động từ "is": "news not" → "news is not".',
            'severity' => 'high',
            'grammar_rule' => 'Be verb in negative sentences'
        ],
        [
            'original_text' => 'secondary student should play sport',
            'suggested_correction' => 'secondary students should play sports',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => '"Student" → "students", "sport" → "sports" (số nhiều).',
            'severity' => 'medium',
            'grammar_rule' => 'Plural nouns for general activities'
        ],
        [
            'original_text' => 'Teacher should teaching',
            'suggested_correction' => 'Teachers should teach',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => '"Teacher" → "teachers", "should teaching" → "should teach" (modal + base form).',
            'severity' => 'high',
            'grammar_rule' => 'Modal verbs + base form'
        ],
        [
            'original_text' => 'I conclusion',
            'suggested_correction' => 'In conclusion',
            'error_type' => 'grammar',
            'category' => 'coherence_cohesion',
            'explanation' => 'Sai cụm từ kết luận: "I conclusion" → "In conclusion".',
            'severity' => 'high',
            'grammar_rule' => 'Conclusion phrases'
        ],
        [
            'original_text' => 'Student no need study',
            'suggested_correction' => 'Students do not need to study',
            'error_type' => 'grammar',
            'category' => 'grammar_accuracy',
            'explanation' => 'Sai cấu trúc phủ định: "no need study" → "do not need to study".',
            'severity' => 'high',
            'grammar_rule' => 'Negative structures with need'
        ]
    ],
    'request_metadata' => [
        'word_count' => 156,
        'processing_time' => 8.45,
        'minimum_words' => 250,
        'word_count_penalty' => true,
        'spelling_errors' => 3,
        'grammar_errors' => 15,
        'total_errors' => 18
    ]
];

$sampleEssay = "International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.

Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.

However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.

In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.

I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.";

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
    header('Content-Type: application/json');
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        // Validate required fields
        if (empty($input['essay']) || empty($input['task_type'])) {
            throw new Exception('Essay text and task type are required');
        }

        if (empty($input['essay_question'])) {
            throw new Exception('Essay question is required');
        }

        // Call the scoring API
        $apiData = [
            'essay' => $input['essay'],
            'task_type' => $input['task_type'],
            'essay_question' => $input['essay_question'],
            'time_limit' => $input['time_limit'] ?? 40
        ];
        
        // Make API call to scoring endpoint
        $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $config['api_endpoint'];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($apiData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'X-Requested-With: XMLHttpRequest'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            throw new Exception('API connection error: ' . $curlError);
        }

        if ($httpCode !== 200) {
            throw new Exception('API returned error code: ' . $httpCode . '. Response: ' . substr($response, 0, 200));
        }

        $result = json_decode($response, true);
        if (!$result) {
            throw new Exception('Invalid API response: ' . substr($response, 0, 200));
        }

        if (isset($result['error']) && $result['error']) {
            throw new Exception($result['message'] ?? 'API error occurred');
        }
        
        // Return successful result
        echo json_encode([
            'success' => true,
            'data' => $result
        ]);
        
    } catch (Exception $e) {
        http_response_code(400);

        // Debug logging if enabled
        if ($config['debug_mode']) {
            error_log('IELTS API Error: ' . $e->getMessage());
            error_log('Request data: ' . json_encode($input ?? []));
        }

        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'debug' => $config['debug_mode'] ? [
                'request_data' => $input ?? [],
                'api_url' => $apiUrl ?? 'not set'
            ] : null
        ]);
    }
    
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($config['app_name']); ?> - Professional AI Assessment</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    
    <script>
        // Pass PHP data to JavaScript
        window.appConfig = <?php echo json_encode($config); ?>;
        window.demoData = <?php echo json_encode($demoData); ?>;
        window.sampleEssay = <?php echo json_encode($sampleEssay); ?>;
    </script>

    <style>
        :root {
            --primary: #667eea;
            --secondary: #764ba2;
            --success: #27ae60;
            --danger: #e74c3c;
            --warning: #f39c12;
            --info: #3498db;
            --dark: #2c3e50;
            --light: #ecf0f1;
            --gradient-primary: linear-gradient(135deg, var(--primary), var(--secondary));
            --gradient-success: linear-gradient(135deg, #11998e, #38ef7d);
            --gradient-danger: linear-gradient(135deg, #ff6b6b, #ee5a24);
            --gradient-warning: linear-gradient(135deg, #f093fb, #f5576c);
            --gradient-info: linear-gradient(135deg, #4facfe, #00f2fe);
            --shadow-soft: 0 10px 40px rgba(0,0,0,0.1);
            --shadow-medium: 0 15px 50px rgba(0,0,0,0.15);
            --shadow-strong: 0 20px 60px rgba(0,0,0,0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            color: var(--dark);
            overflow-x: hidden;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: var(--shadow-strong);
            margin: 20px auto;
            max-width: 1600px;
            overflow: hidden;
            position: relative;
        }

        .header-section {
            background: var(--gradient-primary);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.2);
            letter-spacing: -1px;
        }

        .header-subtitle {
            font-size: 1.3rem;
            opacity: 0.95;
            font-weight: 400;
            max-width: 600px;
            margin: 0 auto;
        }

        .content-section {
            padding: 50px;
        }

        .essay-form-section {
            margin-bottom: 50px;
        }

        .form-card, .tips-card {
            background: white;
            border-radius: 20px;
            padding: 35px;
            box-shadow: var(--shadow-soft);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .tips-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .tips-list {
            list-style: none;
            padding: 0;
        }

        .tips-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.95rem;
        }

        .criteria-mini {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
        }

        .criteria-item-mini {
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }

        .criteria-item-mini:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }

        .criteria-item-mini strong {
            display: block;
            font-size: 0.9rem;
            color: var(--dark);
        }

        .criteria-item-mini small {
            color: #6c757d;
            font-size: 0.8rem;
        }

        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        #wordCount {
            font-weight: 600;
            color: var(--primary);
        }

        .loading-section {
            text-align: center;
            padding: 80px 20px;
        }

        .loading-card {
            background: white;
            border-radius: 24px;
            padding: 50px;
            box-shadow: var(--shadow-medium);
            max-width: 600px;
            margin: 0 auto;
        }

        .loading-animation {
            margin-bottom: 30px;
        }

        .spinner {
            width: 80px;
            height: 80px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 15px;
        }

        .loading-text {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 40px;
        }

        .progress-bar-loading {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 30px;
        }

        .progress-fill-loading {
            height: 100%;
            background: var(--gradient-primary);
            border-radius: 4px;
            width: 0%;
            animation: loadingProgress 8s ease-in-out forwards;
        }

        @keyframes loadingProgress {
            0% { width: 0%; }
            25% { width: 25%; }
            50% { width: 50%; }
            75% { width: 75%; }
            100% { width: 100%; }
        }

        .loading-steps {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }

        .step {
            flex: 1;
            text-align: center;
            padding: 15px;
            border-radius: 12px;
            background: #f8f9fa;
            color: #6c757d;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .step.active {
            background: var(--gradient-primary);
            color: white;
            transform: scale(1.05);
        }

        .step i {
            display: block;
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .floating-actions {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 1000;
        }

        .fab {
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 50%;
            width: 56px;
            height: 56px;
            font-size: 1.4rem;
            box-shadow: var(--shadow-medium);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-strong);
        }

        .progress-indicator {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border-radius: 25px;
            padding: 10px 20px;
            box-shadow: var(--shadow-medium);
            z-index: 1001;
        }

        .progress-text {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 5px;
        }

        .progress-bar {
            width: 200px;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--gradient-success);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .header-title { font-size: 2.5rem; }
            .content-section { padding: 30px 20px; }
            .loading-steps { flex-direction: column; gap: 10px; }
            .form-card, .tips-card { padding: 25px; }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header Section -->
            <div class="header-section">
                <div class="header-content">
                    <h1 class="header-title animate__animated animate__fadeInDown">
                        <i class="fas fa-graduation-cap"></i>
                        <?php echo htmlspecialchars($config['app_name']); ?>
                    </h1>
                    <p class="header-subtitle animate__animated animate__fadeInUp animate__delay-1s">
                        Advanced AI-Powered Assessment with Professional Feedback & Interactive Learning
                    </p>
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <!-- Essay Submission Form -->
                <div class="essay-form-section" id="essayFormSection">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="form-card animate__animated animate__fadeInLeft">
                                <h3 class="section-title">
                                    <i class="fas fa-edit"></i>
                                    Submit Your Essay for Scoring
                                </h3>
                                <form id="essaySubmissionForm">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="taskType" class="form-label">Task Type</label>
                                            <select class="form-select" id="taskType" required>
                                                <option value="">Select task type...</option>
                                                <option value="task2">Task 2 (Essay)</option>
                                                <option value="task1_academic">Task 1 Academic</option>
                                                <option value="task1_general">Task 1 General</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="timeLimit" class="form-label">Time Limit</label>
                                            <select class="form-select" id="timeLimit">
                                                <option value="40">40 minutes (Task 2)</option>
                                                <option value="20">20 minutes (Task 1)</option>
                                                <option value="60">60 minutes (Practice)</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="essayQuestion" class="form-label">
                                            <i class="fas fa-question-circle"></i>
                                            Essay Question/Topic
                                        </label>
                                        <textarea class="form-control" id="essayQuestion" rows="4"
                                                  placeholder="Paste the complete essay question here..." required></textarea>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle"></i>
                                            Copy and paste the exact question from your IELTS test or practice material
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="essayText" class="form-label">Your Essay</label>
                                        <textarea class="form-control" id="essayText" rows="12" 
                                                  placeholder="Write your essay here..." required></textarea>
                                        <div class="form-text">
                                            <span id="wordCount">0</span> words | Minimum 250 words recommended for Task 2
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex gap-3 flex-wrap">
                                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                            <i class="fas fa-paper-plane"></i> Score My Essay
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-lg" onclick="loadDemoEssay()">
                                            <i class="fas fa-flask"></i> Load Demo Essay
                                        </button>
                                        <button type="button" class="btn btn-outline-success btn-lg" onclick="showDemoResults()">
                                            <i class="fas fa-eye"></i> View Demo Results
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-lg" onclick="clearForm()">
                                            <i class="fas fa-trash"></i> Clear
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="tips-card animate__animated animate__fadeInRight">
                                <h4 class="tips-title">
                                    <i class="fas fa-lightbulb"></i>
                                    Writing Tips
                                </h4>
                                <ul class="tips-list">
                                    <li><i class="fas fa-check text-success"></i> Write at least 250 words for Task 2</li>
                                    <li><i class="fas fa-check text-success"></i> Include clear introduction and conclusion</li>
                                    <li><i class="fas fa-check text-success"></i> Use formal academic language</li>
                                    <li><i class="fas fa-check text-success"></i> Support ideas with examples</li>
                                    <li><i class="fas fa-check text-success"></i> Check grammar and spelling</li>
                                    <li><i class="fas fa-check text-success"></i> Manage your time effectively</li>
                                </ul>
                                
                                <div class="scoring-info mt-4">
                                    <h5><i class="fas fa-star"></i> Scoring Criteria</h5>
                                    <div class="criteria-mini">
                                        <div class="criteria-item-mini">
                                            <strong>Task Achievement</strong>
                                            <small>Address all parts of the task</small>
                                        </div>
                                        <div class="criteria-item-mini">
                                            <strong>Coherence & Cohesion</strong>
                                            <small>Logical organization and linking</small>
                                        </div>
                                        <div class="criteria-item-mini">
                                            <strong>Lexical Resource</strong>
                                            <small>Vocabulary range and accuracy</small>
                                        </div>
                                        <div class="criteria-item-mini">
                                            <strong>Grammar Range & Accuracy</strong>
                                            <small>Sentence structures and accuracy</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading Section -->
                <div class="loading-section" id="loadingSection" style="display: none;">
                    <div class="loading-card">
                        <div class="loading-animation">
                            <div class="spinner"></div>
                        </div>
                        <h3 class="loading-title">Analyzing Your Essay...</h3>
                        <p class="loading-text">Our AI is carefully reviewing your writing for grammar, vocabulary, coherence, and task achievement.</p>
                        <div class="loading-progress">
                            <div class="progress-bar-loading">
                                <div class="progress-fill-loading"></div>
                            </div>
                            <div class="loading-steps">
                                <div class="step active" id="step1">
                                    <i class="fas fa-file-text"></i> Processing text
                                </div>
                                <div class="step" id="step2">
                                    <i class="fas fa-search"></i> Analyzing errors
                                </div>
                                <div class="step" id="step3">
                                    <i class="fas fa-chart-bar"></i> Calculating scores
                                </div>
                                <div class="step" id="step4">
                                    <i class="fas fa-check"></i> Generating report
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Section -->
                <div class="results-section" id="resultsSection" style="display: none;">
                    <!-- Results content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Actions -->
    <div class="floating-actions">
        <button class="fab" onclick="newEssay()" title="Submit new essay">
            <i class="fas fa-plus"></i>
        </button>
        <button class="fab" onclick="resetCorrections()" title="Reset corrections">
            <i class="fas fa-undo"></i>
        </button>
        <button class="fab" onclick="scrollToTop()" title="Scroll to top">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>

    <!-- Progress Indicator -->
    <div class="progress-indicator" id="progressIndicator" style="display: none;">
        <div class="progress-text" id="progressText">Corrections Applied: 0/5</div>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let currentEssayData = null;
        let correctedTexts = new Set();
        let activeCorrection = -1;
        let activeTooltip = null;
        let currentEssayText = '';

        // Initialize application
        document.addEventListener('DOMContentLoaded', function() {
            initializeFormHandlers();
            showFormSection();

            // Close tooltip when clicking outside
            document.addEventListener('click', function(event) {
                if (activeTooltip && !event.target.closest('.correction-tooltip') && !event.target.closest('.highlight-error')) {
                    hideCorrectionTooltip();
                }
            });
        });

        // Form handling
        function initializeFormHandlers() {
            document.getElementById('essaySubmissionForm').addEventListener('submit', handleFormSubmission);
            document.getElementById('essayText').addEventListener('input', updateWordCount);
            document.getElementById('taskType').addEventListener('change', updatePromptPlaceholder);
        }

        function updateWordCount() {
            const text = document.getElementById('essayText').value;
            const wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
            document.getElementById('wordCount').textContent = wordCount;

            const wordCountElement = document.getElementById('wordCount');
            if (wordCount < 150) {
                wordCountElement.style.color = '#e74c3c';
            } else if (wordCount < 250) {
                wordCountElement.style.color = '#f39c12';
            } else {
                wordCountElement.style.color = '#27ae60';
            }
        }

        function updatePromptPlaceholder() {
            const taskType = document.getElementById('taskType').value;
            const questionInput = document.getElementById('essayQuestion');
            const timeLimitSelect = document.getElementById('timeLimit');

            const placeholders = {
                'task2': `Some people believe that technology has made our lives easier, while others think it has made our lives more complicated.

Discuss both views and give your own opinion.

Give reasons for your answer and include any relevant examples from your own knowledge or experience.

Write at least 250 words.`,
                'task1_academic': `The chart below shows the percentage of households in owned and rented accommodation in England and Wales between 1918 and 2011.

Summarise the information by selecting and reporting the main features, and make comparisons where relevant.

Write at least 150 words.`,
                'task1_general': `You recently bought a piece of equipment for your kitchen but it did not work. You phoned the shop but no action was taken.

Write a letter to the shop manager. In your letter:
• describe the problem with the equipment
• explain what happened when you phoned the shop
• say what you would like the manager to do

Write at least 150 words.`
            };

            questionInput.placeholder = placeholders[taskType] || 'Paste the complete essay question here...';

            // Update time limit based on task type
            if (taskType === 'task2') {
                timeLimitSelect.value = '40';
            } else if (taskType === 'task1_academic' || taskType === 'task1_general') {
                timeLimitSelect.value = '20';
            }
        }

        async function handleFormSubmission(event) {
            event.preventDefault();

            const formData = {
                essay: document.getElementById('essayText').value.trim(),
                task_type: document.getElementById('taskType').value,
                essay_question: document.getElementById('essayQuestion').value.trim(),
                time_limit: document.getElementById('timeLimit').value
            };

            if (!validateForm(formData)) {
                return;
            }

            showLoadingSection();

            try {
                const result = await callScoringAPI(formData);
                currentEssayData = result.data;
                currentEssayText = formData.essay;
                showResultsSection();

            } catch (error) {
                console.error('Scoring error:', error);
                showErrorMessage(error.message);
                showFormSection();
            }
        }

        function validateForm(formData) {
            if (!formData.essay_question) {
                showErrorMessage('Please enter the essay question/topic.');
                return false;
            }

            if (formData.essay_question.length < 10) {
                showErrorMessage('Essay question is too short. Please enter the complete question.');
                return false;
            }

            if (!formData.essay) {
                showErrorMessage('Please enter your essay text.');
                return false;
            }

            if (formData.essay.split(/\s+/).length < 50) {
                showErrorMessage('Essay is too short. Please write at least 50 words.');
                return false;
            }

            if (!formData.task_type) {
                showErrorMessage('Please select a task type.');
                return false;
            }

            return true;
        }

        async function callScoringAPI(formData) {
            try {
                const response = await fetch('api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.error) {
                    console.error('API Error:', result.message);
                    throw new Error(result.message || 'API error occurred');
                }

                // Return the API result directly (not wrapped in data property)
                return {
                    success: true,
                    data: result
                };

            } catch (error) {
                console.error('Network error:', error);

                // Fallback to demo data in debug mode
                if (window.appConfig.debug_mode) {
                    console.warn('Using demo data due to network error');
                    return {
                        success: true,
                        data: window.demoData
                    };
                }

                throw error;
            }
        }

        // UI Section Management
        function showFormSection() {
            document.getElementById('essayFormSection').style.display = 'block';
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'none';
        }

        function showLoadingSection() {
            document.getElementById('essayFormSection').style.display = 'none';
            document.getElementById('loadingSection').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';

            animateLoadingSteps();
        }

        function showResultsSection() {
            document.getElementById('essayFormSection').style.display = 'none';
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'block';

            // Reset state
            correctedTexts.clear();
            activeCorrection = -1;

            // Populate results
            populateResultsSection();

            // Scroll to results
            document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
        }

        function populateResultsSection() {
            const resultsContainer = document.getElementById('resultsSection');
            const data = currentEssayData || window.demoData;
            const essay = currentEssayText || window.sampleEssay;
            const corrections = data.highlighted_corrections || [];

            resultsContainer.innerHTML = `
                <!-- Score Dashboard -->
                <div class="score-dashboard">
                    <div class="overall-score-card animate__animated animate__zoomIn">
                        <div class="score-number" id="overallScore">${data.overall_band_score || 0}</div>
                        <div class="score-label">Overall Band Score</div>
                        <div class="score-description">
                            ${getBandDescription(data.overall_band_score)}
                        </div>
                    </div>

                    <div class="criteria-breakdown animate__animated animate__fadeInRight">
                        <h3 class="criteria-title">
                            <i class="fas fa-chart-bar"></i>
                            Phân Tích Chi Tiết Từng Tiêu Chí
                        </h3>
                        <div class="criteria-grid" id="criteriaGrid">
                            <!-- Criteria items will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Analysis Section -->
                <div class="analysis-section">
                    <div class="essay-analysis animate__animated animate__fadeInLeft">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-file-alt"></i>
                                Essay Analysis
                            </h3>
                            <div class="view-toggle">
                                <button class="toggle-btn active" onclick="toggleView('highlighted')">
                                    <i class="fas fa-highlighter"></i> Highlighted
                                </button>
                                <button class="toggle-btn" onclick="toggleView('clean')">
                                    <i class="fas fa-file-text"></i> Clean
                                </button>
                            </div>
                        </div>
                        <div class="essay-content" id="essayContent">
                            ${formatEssayWithHighlights(essay, corrections)}
                        </div>
                    </div>

                    <div class="corrections-panel animate__animated animate__fadeInRight">
                        <h3 class="section-title">
                            <i class="fas fa-exclamation-triangle"></i>
                            Key Corrections
                            <span class="badge bg-danger ms-2">${corrections.length}</span>
                        </h3>
                        <div id="correctionsContainer">
                            ${generateCorrectionsHTML(corrections)}
                        </div>
                    </div>
                </div>

                <!-- Statistics Overview -->
                <div class="stats-overview">
                    ${generateStatsHTML(data)}
                </div>

                <!-- Criteria Analysis Table -->
                <div class="criteria-analysis-table animate__animated animate__fadeInUp">
                    <h3 class="analysis-title">
                        <i class="fas fa-table"></i>
                        Bảng Phân Tích Chi Tiết Từng Tiêu Chí
                    </h3>
                    <div class="table-responsive">
                        <table class="criteria-table">
                            <thead>
                                <tr>
                                    <th>Tiêu chí</th>
                                    <th>Đánh giá</th>
                                    <th>Nhận xét chi tiết</th>
                                </tr>
                            </thead>
                            <tbody id="criteriaTableBody">
                                <!-- Table content will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            // Initialize tooltips and interactions
            initializeResultsInteractions();

            // Populate criteria grid
            populateCriteriaGrid(data);

            // Populate criteria analysis table
            populateCriteriaTable(data);
        }

        function populateCriteriaGrid(data) {
            const criteriaGrid = document.getElementById('criteriaGrid');
            const criteriaScores = data.criteria_scores || {};

            const criteriaItems = [
                {
                    key: 'task_achievement',
                    name: 'Task Achievement',
                    feedback: 'Phân tích đề bài và triển khai ý',
                    score: criteriaScores.task_achievement || 0
                },
                {
                    key: 'coherence_cohesion',
                    name: 'Coherence & Cohesion',
                    feedback: 'Tổ chức bài và liên kết ý',
                    score: criteriaScores.coherence_cohesion || 0
                },
                {
                    key: 'lexical_resource',
                    name: 'Lexical Resource',
                    feedback: 'Từ vựng và cách dùng từ',
                    score: criteriaScores.lexical_resource || 0
                },
                {
                    key: 'grammar_accuracy',
                    name: 'Grammar Range & Accuracy',
                    feedback: 'Ngữ pháp và độ chính xác',
                    score: criteriaScores.grammar_accuracy || 0
                }
            ];

            criteriaGrid.innerHTML = criteriaItems.map(item => `
                <div class="criteria-item clickable" onclick="showCriteriaDetail('${item.key}')">
                    <div class="criteria-name">${item.name}</div>
                    <div class="criteria-score">${item.score}</div>
                    <div class="criteria-feedback">${item.feedback}</div>
                    <div class="criteria-action">
                        <i class="fas fa-eye"></i> Xem chi tiết
                    </div>
                </div>
            `).join('');
        }

        function populateCriteriaTable(data) {
            const tableBody = document.getElementById('criteriaTableBody');
            const detailedFeedback = data.detailed_feedback || {};
            const criteriaAnalysis = data.criteria_analysis || {};
            const wordCount = data.request_metadata?.word_count || 0;
            const minWords = 250;

            // Generate criteria data from OpenAI response
            const criteriaData = [
                {
                    name: 'Độ đại yêu cầu (250 từ)',
                    key: 'word_count',
                    score: wordCount < minWords ? '❌ Chưa đạt' : '✅ Đạt',
                    feedback: criteriaAnalysis.word_count_analysis ||
                             `Bài viết có ${wordCount} từ/${minWords} từ yêu cầu. ${wordCount < minWords ? 'Chưa đạt độ dài tối thiểu.' : 'Đạt yêu cầu về độ dài.'}`
                },
                {
                    name: 'Trả lời đúng trọng tâm',
                    key: 'task_achievement',
                    score: getScoreDisplay(detailedFeedback.task_achievement?.score),
                    feedback: criteriaAnalysis.task_response_analysis ||
                             detailedFeedback.task_achievement?.feedback ||
                             'Chưa có phân tích chi tiết từ hệ thống.'
                },
                {
                    name: 'Phát triển ý tưởng',
                    key: 'idea_development',
                    score: getScoreDisplay(detailedFeedback.task_achievement?.score),
                    feedback: detailedFeedback.task_achievement?.issues?.join('. ') ||
                             'Cần cải thiện cách phát triển và trình bày ý tưởng.'
                },
                {
                    name: 'Quan điểm rõ ràng',
                    key: 'position',
                    score: getScoreDisplay(detailedFeedback.coherence_cohesion?.score),
                    feedback: criteriaAnalysis.organization_analysis ||
                             detailedFeedback.coherence_cohesion?.feedback ||
                             'Cần cải thiện cách tổ chức và trình bày quan điểm.'
                },
                {
                    name: 'Từ vựng và chính tả',
                    key: 'vocabulary',
                    score: getScoreDisplay(detailedFeedback.lexical_resource?.score),
                    feedback: criteriaAnalysis.vocabulary_analysis ||
                             detailedFeedback.lexical_resource?.feedback ||
                             'Cần cải thiện từ vựng và kiểm tra chính tả.'
                },
                {
                    name: 'Ngữ pháp',
                    key: 'grammar',
                    score: getScoreDisplay(detailedFeedback.grammar_accuracy?.score),
                    feedback: criteriaAnalysis.grammar_analysis ||
                             detailedFeedback.grammar_accuracy?.feedback ||
                             'Cần cải thiện ngữ pháp và cấu trúc câu.'
                }
            ];

            tableBody.innerHTML = criteriaData.map(item => `
                <tr>
                    <td class="criteria-name-cell">
                        <strong>${item.name}</strong>
                    </td>
                    <td class="criteria-score-cell">
                        <span class="score-badge ${getScoreBadgeClass(item.score)}">${item.score}</span>
                    </td>
                    <td class="criteria-feedback-cell">
                        ${item.feedback}
                    </td>
                </tr>
            `).join('');
        }

        function getScoreDisplay(score) {
            if (!score) return '⚠️ Chưa đánh giá';

            const numScore = parseFloat(score);
            if (numScore >= 7.0) return '✅ Tốt';
            if (numScore >= 6.0) return '✅ Khá';
            if (numScore >= 5.0) return '⚠️ Trung bình';
            if (numScore >= 4.0) return '❌ Yếu';
            return '❌ Kém';
        }

        function getScoreBadgeClass(score) {
            if (score.includes('❌')) return 'score-fail';
            if (score.includes('⚠️')) return 'score-warning';
            if (score.includes('✅')) return 'score-pass';
            return 'score-neutral';
        }

        function getBandDescription(score) {
            const descriptions = {
                9: 'Expert User - Full operational command',
                8: 'Very Good User - Fully operational command',
                7: 'Good User - Operational command',
                6: 'Competent User - Generally effective command',
                5: 'Modest User - Partial command',
                4: 'Limited User - Basic competence',
                3: 'Extremely Limited User - Conveys general meaning',
                2: 'Intermittent User - No real communication',
                1: 'Non-User - No ability to use language'
            };

            const band = Math.floor(score);
            return descriptions[band] || 'Competent User - Good command of the language despite some inaccuracies';
        }

        // Utility functions
        function loadDemoEssay() {
            document.getElementById('essayQuestion').value = `Some people think that secondary school students should study international news as a school subject, while others believe that this is a waste of their time.

Discuss both views and give your own opinion.

Give reasons for your answer and include any relevant examples from your own knowledge or experience.

Write at least 250 words.`;

            document.getElementById('essayText').value = window.sampleEssay;
            document.getElementById('taskType').value = 'task2';
            document.getElementById('timeLimit').value = '40';
            updateWordCount();
        }

        function clearForm() {
            document.getElementById('essaySubmissionForm').reset();
            updateWordCount();
        }

        function showDemoResults() {
            // Load demo data directly
            currentEssayData = window.demoData;
            currentEssayText = window.sampleEssay;

            // Show results section
            showResultsSection();

            // Show success message
            showToast('Demo Mode', 'Showing demo results with interactive features!', 'success');
        }

        function newEssay() {
            correctedTexts.clear();
            activeCorrection = -1;
            currentEssayData = null;
            currentEssayText = '';
            showFormSection();
            clearForm();
        }

        function resetCorrections() {
            correctedTexts.clear();
            activeCorrection = -1;
            populateResultsSection();
            document.getElementById('progressIndicator').style.display = 'none';

            showToast('Reset', 'All corrections have been reset. You can practice again!', 'warning');
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function showErrorMessage(message) {
            showToast('Error', message, 'danger');
        }

        function showToast(title, message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = 'toast-container position-fixed top-0 end-0 p-3';
            toast.innerHTML = `
                <div class="toast show" role="alert">
                    <div class="toast-header">
                        <i class="fas fa-${getToastIcon(type)} text-${type} me-2"></i>
                        <strong class="me-auto">${title}</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }

        function getToastIcon(type) {
            const icons = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        function animateLoadingSteps() {
            const steps = ['step1', 'step2', 'step3', 'step4'];
            let currentStep = 0;

            const interval = setInterval(() => {
                steps.forEach(step => {
                    document.getElementById(step).classList.remove('active');
                });

                if (currentStep < steps.length) {
                    document.getElementById(steps[currentStep]).classList.add('active');
                    currentStep++;
                } else {
                    clearInterval(interval);
                }
            }, 2000);
        }

        // Essay formatting and interactive features
        function formatEssayWithHighlights(essay, corrections) {
            let processedEssay = essay;

            corrections.forEach((correction, index) => {
                const originalText = correction.original_text;
                const isAccepted = correctedTexts.has(index);
                const displayText = isAccepted ? correction.suggested_correction : originalText;
                const cssClass = isAccepted ? 'highlight-error corrected' : 'highlight-error';

                const replacement = `<span class="${cssClass}" data-correction-index="${index}" onclick="showCorrectionTooltip(event, ${index})">${displayText}</span>`;

                const position = processedEssay.indexOf(originalText);
                if (position !== -1) {
                    processedEssay = processedEssay.substring(0, position) +
                                   replacement +
                                   processedEssay.substring(position + originalText.length);
                }
            });

            return processedEssay.replace(/\n/g, '<br><br>');
        }

        function generateCorrectionsHTML(corrections) {
            return corrections.map((correction, index) => {
                const isAccepted = correctedTexts.has(index);
                const cardClass = `correction-card ${index === activeCorrection ? 'active' : ''} ${isAccepted ? 'corrected' : ''}`;

                return `
                <div class="${cardClass}" onclick="selectCorrection(${index})">
                    <div class="correction-header">
                        <div class="correction-number" style="${isAccepted ? 'background: var(--gradient-success);' : ''}">${index + 1}</div>
                        <div style="display: flex; gap: 8px; align-items: center;">
                            ${isAccepted ? '<span class="badge bg-success"><i class="fas fa-check"></i> Applied</span>' : ''}
                            <span class="severity-badge severity-${correction.severity}">
                                <i class="fas fa-${getSeverityIcon(correction.severity)}"></i>
                                ${correction.severity}
                            </span>
                        </div>
                    </div>
                    <div class="text-comparison">
                        <div class="original-text">
                            <strong><i class="fas fa-times"></i> Original:</strong><br>
                            "${correction.original_text}"
                        </div>
                        <div class="suggested-text">
                            <strong><i class="fas fa-check"></i> Suggested:</strong><br>
                            "${correction.suggested_correction}"
                        </div>
                    </div>
                    <div class="correction-explanation">
                        <i class="fas fa-lightbulb"></i> ${correction.explanation}
                    </div>
                    ${!isAccepted ? `
                        <div class="mt-2 text-center">
                            <button class="btn btn-sm btn-success" onclick="event.stopPropagation(); acceptCorrection(${index})">
                                <i class="fas fa-check"></i> Apply Correction
                            </button>
                        </div>
                    ` : ''}
                </div>
            `;
            }).join('');
        }

        function generateStatsHTML(data) {
            const metadata = data.request_metadata || data.metadata || {};
            const statistics = data.statistics || {};
            const wordCount = metadata.word_count || 0;
            const processingTime = metadata.processing_time || 0;
            const correctionsCount = (data.highlighted_corrections || []).length;

            // Calculate dynamic statistics from API data
            const totalErrors = statistics.total_errors_count || correctionsCount;
            const spellingErrors = statistics.spelling_errors_count || 0;
            const grammarErrors = statistics.grammar_errors_count || 0;
            const accuracyPercentage = statistics.accuracy_percentage || calculateAccuracy(data);
            const cleanRate = statistics.clean_rate_percentage || calculateCleanRate(data);
            const cefrLevel = statistics.cefr_level || calculateCEFRLevel(data);

            return `
                <div class="stat-card animate__animated animate__fadeInUp">
                    <div class="stat-icon">
                        <i class="fas fa-file-word"></i>
                    </div>
                    <div class="stat-number">${wordCount}</div>
                    <div class="stat-label">Word Count</div>
                </div>
                <div class="stat-card animate__animated animate__fadeInUp animate__delay-1s">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="stat-number">${totalErrors}</div>
                    <div class="stat-label">Total Errors</div>
                </div>
                <div class="stat-card animate__animated animate__fadeInUp animate__delay-2s">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-number">${cleanRate}%</div>
                    <div class="stat-label">Clean Rate</div>
                </div>
                <div class="stat-card animate__animated animate__fadeInUp animate__delay-3s">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number">${processingTime.toFixed(1)}s</div>
                    <div class="stat-label">Process Time</div>
                </div>
                <div class="stat-card animate__animated animate__fadeInUp animate__delay-4s">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-number">${cefrLevel}</div>
                    <div class="stat-label">CEFR Level</div>
                </div>
                <div class="stat-card animate__animated animate__fadeInUp animate__delay-5s">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-number">${accuracyPercentage}%</div>
                    <div class="stat-label">Accuracy</div>
                </div>
            `;
        }

        function calculateAccuracy(data) {
            const wordCount = data.request_metadata?.word_count || 0;
            const errorsCount = (data.highlighted_corrections || []).length;
            if (wordCount === 0) return 0;

            // Calculate accuracy as percentage of words without errors
            const accuracy = Math.max(0, Math.round(((wordCount - errorsCount) / wordCount) * 100));
            return accuracy;
        }

        function calculateCleanRate(data) {
            const totalSentences = estimateSentenceCount(data.request_metadata?.essay_length || 0);
            const errorsCount = (data.highlighted_corrections || []).length;
            if (totalSentences === 0) return 100;

            // Calculate clean rate as percentage of sentences without errors
            const cleanRate = Math.max(0, Math.round(((totalSentences - errorsCount) / totalSentences) * 100));
            return cleanRate;
        }

        function calculateCEFRLevel(data) {
            const overallScore = data.overall_band_score || 0;

            // Map IELTS band scores to CEFR levels
            if (overallScore >= 8.5) return 'C2';
            if (overallScore >= 7.0) return 'C1';
            if (overallScore >= 5.5) return 'B2';
            if (overallScore >= 4.0) return 'B1';
            if (overallScore >= 3.0) return 'A2';
            return 'A1';
        }

        function estimateSentenceCount(textLength) {
            // Rough estimate: average sentence length is about 15-20 words
            // Average word length is about 5 characters
            return Math.max(1, Math.round(textLength / 100));
        }

        function getSeverityIcon(severity) {
            const icons = {
                'high': 'exclamation-triangle',
                'medium': 'exclamation-circle',
                'low': 'info-circle'
            };
            return icons[severity] || 'info-circle';
        }

        function initializeResultsInteractions() {
            // Add CSS for interactive elements
            if (!document.getElementById('interactiveStyles')) {
                const style = document.createElement('style');
                style.id = 'interactiveStyles';
                style.textContent = `
                    .highlight-error {
                        background: linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2));
                        padding: 2px 6px;
                        border-radius: 6px;
                        border-bottom: 2px solid var(--danger);
                        cursor: pointer;
                        transition: all 0.3s ease;
                        position: relative;
                        display: inline-block;
                    }
                    .highlight-error:hover {
                        background: linear-gradient(135deg, rgba(231, 76, 60, 0.3), rgba(192, 57, 43, 0.3));
                        transform: scale(1.02);
                    }
                    .highlight-error.corrected {
                        background: linear-gradient(135deg, rgba(39, 174, 96, 0.2), rgba(46, 204, 113, 0.2));
                        border-bottom: 2px solid var(--success);
                        color: var(--success);
                        font-weight: 600;
                    }
                    .correction-card {
                        background: #f8f9fa;
                        border-radius: 16px;
                        padding: 20px;
                        margin-bottom: 16px;
                        border-left: 4px solid var(--warning);
                        transition: all 0.3s ease;
                        cursor: pointer;
                    }
                    .correction-card:hover {
                        transform: translateX(8px);
                        box-shadow: var(--shadow-soft);
                        background: white;
                    }
                    .correction-card.active {
                        border-left-color: var(--primary);
                        background: white;
                        box-shadow: var(--shadow-soft);
                        transform: translateX(8px);
                    }
                    .correction-number {
                        background: var(--gradient-warning);
                        color: white;
                        border-radius: 50%;
                        width: 32px;
                        height: 32px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: 700;
                        font-size: 0.9rem;
                    }
                    .severity-badge {
                        padding: 6px 12px;
                        border-radius: 20px;
                        font-size: 0.75rem;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }
                    .severity-high {
                        background: var(--gradient-danger);
                        color: white;
                    }
                    .severity-medium {
                        background: var(--gradient-warning);
                        color: white;
                    }
                    .severity-low {
                        background: var(--gradient-info);
                        color: white;
                    }
                    .original-text, .suggested-text {
                        padding: 12px 16px;
                        border-radius: 12px;
                        font-family: 'Courier New', monospace;
                        font-size: 0.9rem;
                        margin: 8px 0;
                    }
                    .original-text {
                        background: linear-gradient(135deg, #ffe6e6, #ffcccc);
                        border-left: 4px solid var(--danger);
                        color: #c0392b;
                    }
                    .suggested-text {
                        background: linear-gradient(135deg, #e6ffe6, #ccffcc);
                        border-left: 4px solid var(--success);
                        color: #27ae60;
                    }
                    .correction-explanation {
                        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
                        padding: 15px;
                        border-radius: 12px;
                        font-size: 0.9rem;
                        color: var(--dark);
                        border-left: 4px solid var(--info);
                    }
                    .stat-card {
                        background: white;
                        border-radius: 16px;
                        padding: 25px;
                        text-align: center;
                        box-shadow: var(--shadow-soft);
                        border: 1px solid #e9ecef;
                        transition: all 0.3s ease;
                    }
                    .stat-card:hover {
                        transform: translateY(-5px);
                        box-shadow: var(--shadow-medium);
                    }
                    .stat-icon {
                        font-size: 2.5rem;
                        background: var(--gradient-primary);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                        margin-bottom: 15px;
                    }
                    .stat-number {
                        font-size: 2.5rem;
                        font-weight: 800;
                        background: var(--gradient-primary);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                        margin-bottom: 8px;
                    }
                    .stat-label {
                        font-size: 0.9rem;
                        color: #6c757d;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }
                    .score-dashboard {
                        display: grid;
                        grid-template-columns: 1fr 2fr;
                        gap: 40px;
                        margin-bottom: 50px;
                    }
                    .overall-score-card {
                        background: var(--gradient-success);
                        color: white;
                        border-radius: 24px;
                        padding: 40px;
                        text-align: center;
                        position: relative;
                        overflow: hidden;
                        box-shadow: var(--shadow-medium);
                    }
                    .score-number {
                        font-size: 6rem;
                        font-weight: 900;
                        margin-bottom: 15px;
                        text-shadow: 0 6px 12px rgba(0,0,0,0.3);
                    }
                    .score-label {
                        font-size: 1.6rem;
                        font-weight: 600;
                        opacity: 0.95;
                    }
                    .score-description {
                        font-size: 1rem;
                        opacity: 0.9;
                        margin-top: 15px;
                    }
                    .criteria-breakdown {
                        background: white;
                        border-radius: 20px;
                        padding: 30px;
                        box-shadow: var(--shadow-soft);
                    }
                    .criteria-title {
                        font-size: 1.4rem;
                        font-weight: 700;
                        color: var(--dark);
                        margin-bottom: 25px;
                        display: flex;
                        align-items: center;
                        gap: 12px;
                    }
                    .criteria-grid {
                        display: grid;
                        grid-template-columns: repeat(2, 1fr);
                        gap: 20px;
                    }
                    .criteria-item {
                        background: #f8f9fa;
                        border-radius: 16px;
                        padding: 20px;
                        border-left: 4px solid var(--info);
                        transition: all 0.3s ease;
                    }
                    .criteria-item:hover {
                        transform: translateY(-3px);
                        box-shadow: var(--shadow-soft);
                        border-left-color: var(--primary);
                    }

                    .criteria-item.clickable {
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }

                    .criteria-item.clickable:hover {
                        background: white;
                        transform: translateY(-5px);
                        box-shadow: var(--shadow-medium);
                    }

                    .criteria-action {
                        margin-top: 10px;
                        font-size: 0.85rem;
                        color: var(--primary);
                        font-weight: 600;
                        display: flex;
                        align-items: center;
                        gap: 5px;
                    }
                    .criteria-name {
                        font-size: 0.95rem;
                        font-weight: 600;
                        color: var(--dark);
                        margin-bottom: 8px;
                    }
                    .criteria-score {
                        font-size: 2.2rem;
                        font-weight: 800;
                        background: var(--gradient-info);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    }
                    .criteria-feedback {
                        font-size: 0.85rem;
                        color: #6c757d;
                        margin-top: 5px;
                    }
                    .analysis-section {
                        display: grid;
                        grid-template-columns: 1.5fr 1fr;
                        gap: 40px;
                        margin-bottom: 40px;
                    }
                    .essay-analysis {
                        background: white;
                        border-radius: 20px;
                        padding: 35px;
                        box-shadow: var(--shadow-soft);
                    }
                    .section-header {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 25px;
                    }
                    .view-toggle {
                        display: flex;
                        background: #f8f9fa;
                        border-radius: 12px;
                        padding: 4px;
                    }
                    .toggle-btn {
                        background: none;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 8px;
                        font-size: 0.9rem;
                        font-weight: 500;
                        color: #6c757d;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .toggle-btn.active {
                        background: var(--primary);
                        color: white;
                        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
                    }
                    .essay-content {
                        background: #f8f9fa;
                        border-radius: 16px;
                        padding: 30px;
                        line-height: 1.8;
                        font-size: 1.05rem;
                        border: 2px solid #e9ecef;
                        min-height: 400px;
                    }
                    .corrections-panel {
                        background: white;
                        border-radius: 20px;
                        padding: 30px;
                        box-shadow: var(--shadow-soft);
                        max-height: 600px;
                        overflow-y: auto;
                    }
                    .stats-overview {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 25px;
                        margin-top: 40px;
                    }
                    /* Tooltip Styles */
                    .correction-tooltip {
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%) scale(0.8);
                        background: white;
                        border-radius: 16px;
                        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                        border: 2px solid var(--primary);
                        min-width: 450px;
                        max-width: 550px;
                        z-index: 1500;
                        opacity: 0;
                        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
                        pointer-events: none;
                    }

                    .correction-tooltip.show {
                        opacity: 1;
                        transform: translate(-50%, -50%) scale(1);
                        pointer-events: all;
                    }

                    /* Tooltip backdrop */
                    .tooltip-backdrop {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.3);
                        z-index: 1400;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    }

                    .tooltip-backdrop.show {
                        opacity: 1;
                    }

                    .tooltip-header {
                        background: var(--gradient-primary);
                        color: white;
                        padding: 15px 20px;
                        border-radius: 14px 14px 0 0;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    }

                    .tooltip-title {
                        font-weight: 600;
                        font-size: 1rem;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .tooltip-close {
                        background: none;
                        border: none;
                        color: white;
                        font-size: 1.2rem;
                        cursor: pointer;
                        padding: 5px;
                        border-radius: 50%;
                        transition: all 0.3s ease;
                    }

                    .tooltip-close:hover {
                        background: rgba(255,255,255,0.2);
                    }

                    .tooltip-content {
                        padding: 20px;
                    }

                    .tooltip-original, .tooltip-suggested {
                        padding: 12px 16px;
                        border-radius: 12px;
                        margin: 10px 0;
                        font-family: 'Courier New', monospace;
                        font-size: 0.9rem;
                    }

                    .tooltip-original {
                        background: linear-gradient(135deg, #ffe6e6, #ffcccc);
                        border-left: 4px solid var(--danger);
                        color: #c0392b;
                    }

                    .tooltip-suggested {
                        background: linear-gradient(135deg, #e6ffe6, #ccffcc);
                        border-left: 4px solid var(--success);
                        color: #27ae60;
                    }

                    .tooltip-explanation {
                        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
                        padding: 15px;
                        border-radius: 12px;
                        font-size: 0.9rem;
                        color: var(--dark);
                        border-left: 4px solid var(--info);
                        margin: 15px 0;
                    }

                    .tooltip-actions {
                        padding: 15px 20px;
                        border-top: 1px solid #e9ecef;
                        display: flex;
                        gap: 10px;
                        justify-content: center;
                    }

                    .tooltip-btn {
                        padding: 10px 20px;
                        border: none;
                        border-radius: 25px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .btn-accept {
                        background: var(--gradient-success);
                        color: white;
                    }

                    .btn-accept:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
                    }

                    .btn-dismiss {
                        background: #f8f9fa;
                        color: #6c757d;
                        border: 1px solid #dee2e6;
                    }

                    .btn-dismiss:hover {
                        background: #e9ecef;
                        transform: translateY(-2px);
                    }

                    /* Celebration Modal */
                    .celebration-modal {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.8);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 2000;
                        opacity: 0;
                        transition: all 0.3s ease;
                    }

                    .celebration-modal.show {
                        opacity: 1;
                    }

                    .celebration-content {
                        background: white;
                        border-radius: 24px;
                        padding: 50px;
                        text-align: center;
                        max-width: 500px;
                        margin: 20px;
                        transform: scale(0.8);
                        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
                    }

                    .celebration-modal.show .celebration-content {
                        transform: scale(1);
                    }

                    .celebration-animation {
                        font-size: 5rem;
                        background: var(--gradient-warning);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                        margin-bottom: 20px;
                        animation: bounce 2s infinite;
                    }

                    @keyframes bounce {
                        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                        40% { transform: translateY(-20px); }
                        60% { transform: translateY(-10px); }
                    }

                    .celebration-title {
                        font-size: 2.5rem;
                        font-weight: 800;
                        background: var(--gradient-success);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                        margin-bottom: 20px;
                    }

                    .celebration-text {
                        font-size: 1.2rem;
                        color: #6c757d;
                        margin-bottom: 30px;
                        line-height: 1.6;
                    }

                    .celebration-actions {
                        display: flex;
                        gap: 15px;
                        justify-content: center;
                        flex-wrap: wrap;
                    }

                    @media (max-width: 1200px) {
                        .score-dashboard { grid-template-columns: 1fr; }
                        .analysis-section { grid-template-columns: 1fr; }
                        .criteria-grid { grid-template-columns: 1fr; }
                    }

                    /* Criteria Detail Modal */
                    .criteria-detail-modal {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.8);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 2000;
                        opacity: 0;
                        transition: all 0.3s ease;
                    }

                    .criteria-detail-modal.show {
                        opacity: 1;
                    }

                    .criteria-detail-content {
                        background: white;
                        border-radius: 20px;
                        max-width: 800px;
                        max-height: 90vh;
                        margin: 20px;
                        overflow: hidden;
                        transform: scale(0.8);
                        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
                    }

                    .criteria-detail-modal.show .criteria-detail-content {
                        transform: scale(1);
                    }

                    .criteria-detail-header {
                        background: var(--gradient-primary);
                        color: white;
                        padding: 25px 30px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    }

                    .criteria-detail-title {
                        font-size: 1.5rem;
                        font-weight: 700;
                        margin: 0;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }

                    .criteria-detail-score {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        background: rgba(255,255,255,0.2);
                        padding: 10px 15px;
                        border-radius: 15px;
                    }

                    .score-label {
                        font-size: 0.9rem;
                        opacity: 0.9;
                    }

                    .score-value {
                        font-size: 1.8rem;
                        font-weight: 800;
                    }

                    .criteria-close {
                        background: none;
                        border: none;
                        color: white;
                        font-size: 1.5rem;
                        cursor: pointer;
                        padding: 10px;
                        border-radius: 50%;
                        transition: all 0.3s ease;
                    }

                    .criteria-close:hover {
                        background: rgba(255,255,255,0.2);
                    }

                    .criteria-detail-body {
                        padding: 30px;
                        max-height: 60vh;
                        overflow-y: auto;
                    }

                    .feedback-section, .issues-section, .improvements-section, .outline-section, .highlight-section {
                        margin-bottom: 25px;
                        padding-bottom: 20px;
                        border-bottom: 1px solid #e9ecef;
                    }

                    .feedback-section:last-child, .issues-section:last-child, .improvements-section:last-child, .outline-section:last-child, .highlight-section:last-child {
                        border-bottom: none;
                        margin-bottom: 0;
                    }

                    .criteria-detail-body h3 {
                        font-size: 1.1rem;
                        font-weight: 600;
                        color: var(--dark);
                        margin-bottom: 15px;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .feedback-text {
                        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
                        padding: 15px 20px;
                        border-radius: 12px;
                        border-left: 4px solid var(--info);
                        font-size: 1rem;
                        line-height: 1.6;
                        color: var(--dark);
                    }

                    .issues-list, .improvements-list, .outline-list {
                        list-style: none;
                        padding: 0;
                    }

                    .issues-list li, .improvements-list li {
                        padding: 10px 15px;
                        margin: 8px 0;
                        border-radius: 10px;
                        display: flex;
                        align-items: flex-start;
                        gap: 10px;
                        font-size: 0.95rem;
                        line-height: 1.5;
                    }

                    .issues-list li {
                        background: linear-gradient(135deg, #ffe6e6, #ffcccc);
                        border-left: 4px solid var(--danger);
                    }

                    .improvements-list li {
                        background: linear-gradient(135deg, #e6ffe6, #ccffcc);
                        border-left: 4px solid var(--success);
                    }

                    .outline-list {
                        background: #f8f9fa;
                        padding: 20px;
                        border-radius: 12px;
                        border-left: 4px solid var(--warning);
                    }

                    .outline-list li {
                        padding: 8px 0;
                        font-size: 0.95rem;
                        line-height: 1.5;
                        border-bottom: 1px solid #e9ecef;
                    }

                    .outline-list li:last-child {
                        border-bottom: none;
                    }

                    .highlight-section {
                        text-align: center;
                        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
                        padding: 20px;
                        border-radius: 12px;
                        border-left: 4px solid var(--warning);
                    }

                    /* Criteria Analysis Table */
                    .criteria-analysis-table {
                        background: white;
                        border-radius: 20px;
                        padding: 30px;
                        margin: 30px 0;
                        box-shadow: var(--shadow-soft);
                        border: 1px solid #e9ecef;
                    }

                    .analysis-title {
                        font-size: 1.5rem;
                        font-weight: 700;
                        color: var(--dark);
                        margin-bottom: 25px;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        border-bottom: 3px solid var(--primary);
                        padding-bottom: 15px;
                    }

                    .table-responsive {
                        overflow-x: auto;
                        border-radius: 12px;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    }

                    .criteria-table {
                        width: 100%;
                        border-collapse: collapse;
                        font-size: 0.95rem;
                        background: white;
                    }

                    .criteria-table thead {
                        background: var(--gradient-primary);
                        color: white;
                    }

                    .criteria-table th {
                        padding: 18px 15px;
                        text-align: left;
                        font-weight: 600;
                        font-size: 1rem;
                        border: none;
                    }

                    .criteria-table th:first-child {
                        border-radius: 12px 0 0 0;
                        width: 25%;
                    }

                    .criteria-table th:nth-child(2) {
                        width: 15%;
                        text-align: center;
                    }

                    .criteria-table th:last-child {
                        border-radius: 0 12px 0 0;
                        width: 60%;
                    }

                    .criteria-table td {
                        padding: 20px 15px;
                        border-bottom: 1px solid #e9ecef;
                        vertical-align: top;
                        line-height: 1.6;
                    }

                    .criteria-table tbody tr:hover {
                        background: #f8f9fa;
                        transition: all 0.3s ease;
                    }

                    .criteria-table tbody tr:last-child td {
                        border-bottom: none;
                    }

                    .criteria-name-cell {
                        font-weight: 600;
                        color: var(--dark);
                        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                    }

                    .criteria-score-cell {
                        text-align: center;
                        font-weight: 600;
                    }

                    .criteria-feedback-cell {
                        color: #495057;
                        line-height: 1.7;
                    }

                    .score-badge {
                        padding: 8px 15px;
                        border-radius: 20px;
                        font-weight: 600;
                        font-size: 0.9rem;
                        display: inline-block;
                        min-width: 80px;
                    }

                    .score-fail {
                        background: linear-gradient(135deg, #ffe6e6, #ffcccc);
                        color: #c0392b;
                        border: 2px solid #e74c3c;
                    }

                    .score-warning {
                        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
                        color: #d68910;
                        border: 2px solid #f39c12;
                    }

                    .score-pass {
                        background: linear-gradient(135deg, #e6ffe6, #ccffcc);
                        color: #27ae60;
                        border: 2px solid #2ecc71;
                    }

                    .score-neutral {
                        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                        color: #6c757d;
                        border: 2px solid #adb5bd;
                    }

                    /* Error Type Badge */
                    .error-type-badge {
                        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
                        color: #1976d2;
                        padding: 4px 8px;
                        border-radius: 12px;
                        font-size: 0.75rem;
                        font-weight: 600;
                        text-transform: uppercase;
                        border: 1px solid #2196f3;
                    }

                    /* Pulse Animation */
                    @keyframes pulse {
                        0% { transform: scale(1); }
                        50% { transform: scale(1.05); }
                        100% { transform: scale(1); }
                    }

                    /* Grammar Rule, Spelling Rule Styles */
                    .grammar-rule, .spelling-rule {
                        background: linear-gradient(135deg, #f3e5f5, #e1bee7);
                        padding: 10px 15px;
                        border-radius: 8px;
                        margin-top: 10px;
                        border-left: 4px solid #9c27b0;
                        font-size: 0.9rem;
                    }

                    .spelling-rule {
                        background: linear-gradient(135deg, #fff3e0, #ffe0b2);
                        border-left-color: #ff9800;
                    }

                    @media (max-width: 768px) {
                        .correction-tooltip {
                            min-width: 300px;
                            max-width: 90vw;
                        }
                        .celebration-content {
                            padding: 30px 20px;
                        }
                        .celebration-title {
                            font-size: 2rem;
                        }
                        .criteria-detail-content {
                            max-width: 95vw;
                            margin: 10px;
                        }
                        .criteria-detail-header {
                            padding: 20px;
                            flex-direction: column;
                            gap: 15px;
                            text-align: center;
                        }
                        .criteria-detail-body {
                            padding: 20px;
                        }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // Interactive correction functions
        function showCorrectionTooltip(event, index) {
            event.stopPropagation(); // Prevent event bubbling

            if (correctedTexts.has(index)) return; // Don't show tooltip for already corrected text

            // If tooltip is already showing for this correction, hide it
            if (activeTooltip && activeTooltip.dataset.correctionIndex == index) {
                hideCorrectionTooltip();
                return;
            }

            // Hide any existing tooltip
            hideCorrectionTooltip();

            const data = currentEssayData || window.demoData;
            const correction = data.highlighted_corrections[index];
            if (!correction) return;

            const element = event.target;

            // Create tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'correction-tooltip';
            tooltip.dataset.correctionIndex = index;
            tooltip.innerHTML = `
                <div class="tooltip-header">
                    <div class="tooltip-title">
                        <i class="fas fa-lightbulb"></i>
                        Correction Suggestion
                    </div>
                    <button class="tooltip-close" onclick="hideCorrectionTooltip()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="tooltip-content">
                    <div class="tooltip-original">
                        <strong><i class="fas fa-times text-danger"></i> Original:</strong><br>
                        "${correction.original_text}"
                    </div>
                    <div class="tooltip-suggested">
                        <strong><i class="fas fa-check text-success"></i> Suggested:</strong><br>
                        "${correction.suggested_correction}"
                    </div>
                    <div class="tooltip-explanation">
                        <i class="fas fa-info-circle"></i> ${correction.explanation}
                    </div>
                </div>
                <div class="tooltip-actions">
                    <button class="tooltip-btn btn-accept" onclick="acceptCorrection(${index})">
                        <i class="fas fa-check"></i> Accept
                    </button>
                    <button class="tooltip-btn btn-dismiss" onclick="hideCorrectionTooltip()">
                        <i class="fas fa-times"></i> Dismiss
                    </button>
                </div>
            `;

            // Create backdrop
            const backdrop = document.createElement('div');
            backdrop.className = 'tooltip-backdrop';
            backdrop.onclick = hideCorrectionTooltip;

            // Add to document
            document.body.appendChild(backdrop);
            document.body.appendChild(tooltip);
            activeTooltip = tooltip;

            // Show tooltip with animation
            setTimeout(() => {
                backdrop.classList.add('show');
                tooltip.classList.add('show');
            }, 10);
        }

        function hideCorrectionTooltip() {
            if (activeTooltip) {
                const backdrop = document.querySelector('.tooltip-backdrop');

                // Hide with animation
                activeTooltip.classList.remove('show');
                if (backdrop) {
                    backdrop.classList.remove('show');
                }

                setTimeout(() => {
                    // Remove tooltip
                    if (activeTooltip && activeTooltip.parentNode) {
                        activeTooltip.parentNode.removeChild(activeTooltip);
                    }

                    // Remove backdrop
                    if (backdrop && backdrop.parentNode) {
                        backdrop.parentNode.removeChild(backdrop);
                    }

                    activeTooltip = null;
                }, 300);
            }
        }

        function selectCorrection(index) {
            activeCorrection = index;
            // Re-render corrections to show active state
            const data = currentEssayData || window.demoData;
            document.getElementById('correctionsContainer').innerHTML = generateCorrectionsHTML(data.highlighted_corrections || []);
        }

        function acceptCorrection(index) {
            correctedTexts.add(index);
            hideCorrectionTooltip();

            // Update the essay display
            const data = currentEssayData || window.demoData;
            const essay = currentEssayText || window.sampleEssay;
            const corrections = data.highlighted_corrections || [];

            document.getElementById('essayContent').innerHTML = formatEssayWithHighlights(essay, corrections);

            // Update corrections panel
            document.getElementById('correctionsContainer').innerHTML = generateCorrectionsHTML(corrections);

            // Update progress
            updateProgress();

            // Show success feedback
            showAcceptedFeedback(index);

            // Check if all corrections are accepted
            if (correctedTexts.size === corrections.length) {
                showCompletionCelebration();
            }
        }

        function updateProgress() {
            const data = currentEssayData || window.demoData;
            const total = (data.highlighted_corrections || []).length;
            const completed = correctedTexts.size;
            const percentage = total > 0 ? (completed / total) * 100 : 0;

            // Show progress indicator
            const progressIndicator = document.getElementById('progressIndicator');
            if (progressIndicator) {
                progressIndicator.style.display = 'block';
                document.getElementById('progressText').textContent = `Corrections Applied: ${completed}/${total}`;
                document.getElementById('progressFill').style.width = `${percentage}%`;
            }
        }

        function showAcceptedFeedback(index) {
            const data = currentEssayData || window.demoData;
            const correction = data.highlighted_corrections[index];

            showToast('Correction Applied', `"${correction.original_text}" → "${correction.suggested_correction}"`, 'success');
        }

        function showCompletionCelebration() {
            // Create celebration modal
            const modal = document.createElement('div');
            modal.className = 'celebration-modal';
            modal.innerHTML = `
                <div class="celebration-content">
                    <div class="celebration-animation">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h2 class="celebration-title">Congratulations! 🎉</h2>
                    <p class="celebration-text">
                        You've successfully applied all corrections!<br>
                        Your essay has been improved significantly.
                    </p>
                    <div class="celebration-actions">
                        <button class="btn btn-primary" onclick="closeCelebration()">
                            <i class="fas fa-eye"></i> Review Final Essay
                        </button>
                        <button class="btn btn-outline-secondary" onclick="resetCorrections()">
                            <i class="fas fa-redo"></i> Practice Again
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Show modal with animation
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);

            // Auto-hide after 10 seconds
            setTimeout(() => {
                closeCelebration();
            }, 10000);
        }

        function closeCelebration() {
            const modal = document.querySelector('.celebration-modal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        function toggleView(view) {
            // Toggle between highlighted and clean view
            const data = currentEssayData || window.demoData;
            const essay = currentEssayText || window.sampleEssay;
            const corrections = data.highlighted_corrections || [];

            // Update button states
            document.querySelectorAll('.toggle-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Find the clicked button and make it active
            const clickedBtn = event ? event.target : document.querySelector(`[onclick="toggleView('${view}')"]`);
            if (clickedBtn) {
                clickedBtn.classList.add('active');
            }

            // Update essay content
            const essayContent = document.getElementById('essayContent');
            if (view === 'highlighted') {
                essayContent.innerHTML = formatEssayWithHighlights(essay, corrections);
            } else {
                essayContent.innerHTML = essay.replace(/\n/g, '<br><br>');
            }

            // Hide any active tooltip
            hideCorrectionTooltip();
        }

        // Criteria detail functions
        function showCriteriaDetail(criteriaType) {
            const data = currentEssayData || window.demoData;
            const criteriaData = data.detailed_feedback?.[criteriaType];

            if (!criteriaData) {
                showToast('Thông báo', 'Không có dữ liệu chi tiết cho tiêu chí này', 'info');
                return;
            }

            // Create criteria detail modal
            const modal = document.createElement('div');
            modal.className = 'criteria-detail-modal';
            modal.innerHTML = `
                <div class="criteria-detail-content">
                    <div class="criteria-detail-header">
                        <h2 class="criteria-detail-title">
                            <i class="fas fa-chart-line"></i>
                            ${getCriteriaTitle(criteriaType)}
                        </h2>
                        <div class="criteria-detail-score">
                            <span class="score-label">Điểm:</span>
                            <span class="score-value">${criteriaData.score}</span>
                        </div>
                        <button class="criteria-close" onclick="closeCriteriaDetail()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="criteria-detail-body">
                        <div class="feedback-section">
                            <h3><i class="fas fa-comment"></i> Nhận Xét Tổng Quan</h3>
                            <p class="feedback-text">${criteriaData.feedback}</p>
                        </div>

                        <div class="issues-section">
                            <h3><i class="fas fa-exclamation-triangle"></i> Các Vấn Đề Cần Cải Thiện</h3>
                            <ul class="issues-list">
                                ${criteriaData.issues.map(issue => `<li><i class="fas fa-times text-danger"></i> ${issue}</li>`).join('')}
                            </ul>
                        </div>

                        <div class="improvements-section">
                            <h3><i class="fas fa-lightbulb"></i> Hướng Dẫn Cải Thiện</h3>
                            <ul class="improvements-list">
                                ${criteriaData.improvements.map(improvement => `<li><i class="fas fa-check text-success"></i> ${improvement}</li>`).join('')}
                            </ul>
                        </div>

                        ${criteriaData.outline_suggestion ? `
                            <div class="outline-section">
                                <h3><i class="fas fa-list"></i> Dàn Ý Mẫu</h3>
                                <ol class="outline-list">
                                    ${criteriaData.outline_suggestion.map(point => `<li>${point}</li>`).join('')}
                                </ol>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Show modal with animation
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        function getCriteriaTitle(criteriaType) {
            const titles = {
                'task_achievement': 'Task Achievement',
                'coherence_cohesion': 'Coherence & Cohesion',
                'lexical_resource': 'Lexical Resource',
                'grammar_accuracy': 'Grammar Range & Accuracy'
            };
            return titles[criteriaType] || criteriaType;
        }

        function closeCriteriaDetail() {
            const modal = document.querySelector('.criteria-detail-modal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        function highlightCriteriaErrors(criteriaType) {
            const data = currentEssayData || window.demoData;
            const essay = currentEssayText || window.sampleEssay;
            const corrections = data.highlighted_corrections || [];

            // Filter corrections by criteria type
            const criteriaCorrections = corrections.filter(correction => {
                // Check both category and error_type for better filtering
                return correction.category === criteriaType ||
                       correction.error_type === criteriaType ||
                       (criteriaType === 'grammar_accuracy' && correction.error_type === 'grammar') ||
                       (criteriaType === 'lexical_resource' && (correction.error_type === 'vocabulary' || correction.error_type === 'spelling')) ||
                       (criteriaType === 'coherence_cohesion' && correction.error_type === 'coherence_cohesion') ||
                       (criteriaType === 'task_achievement' && correction.error_type === 'task_achievement');
            });

            console.log(`Filtering ${corrections.length} corrections for ${criteriaType}:`, criteriaCorrections);

            // Close criteria detail modal
            closeCriteriaDetail();

            // Update essay content with criteria-specific highlighting
            const essayContent = document.getElementById('essayContent');
            if (essayContent) {
                essayContent.innerHTML = formatEssayWithCriteriaHighlights(essay, criteriaCorrections, criteriaType);
            }

            // Update corrections panel
            const correctionsContainer = document.getElementById('correctionsContainer');
            if (correctionsContainer) {
                correctionsContainer.innerHTML = generateCriteriaCorrectionsHTML(criteriaCorrections, criteriaType);
            }

            // Show success message
            const criteriaName = getCriteriaTitle(criteriaType);
            showToast('Thành công', `Đã highlight ${criteriaCorrections.length} lỗi ${criteriaName}`, 'success');
        }

        function formatEssayWithCriteriaHighlights(essay, corrections, criteriaType) {
            let processedEssay = essay;

            // Define colors for different criteria
            const criteriaColors = {
                'task_achievement': 'rgba(255, 193, 7, 0.3)', // Yellow for task achievement
                'coherence_cohesion': 'rgba(156, 39, 176, 0.3)', // Purple for coherence
                'lexical_resource': 'rgba(255, 152, 0, 0.3)', // Orange for vocabulary
                'grammar_accuracy': 'rgba(33, 150, 243, 0.3)' // Blue for grammar
            };

            const borderColors = {
                'task_achievement': '#ffc107',
                'coherence_cohesion': '#9c27b0',
                'lexical_resource': '#ff9800',
                'grammar_accuracy': '#2196f3'
            };

            corrections.forEach((correction, index) => {
                const originalText = correction.original_text;
                const isAccepted = correctedTexts.has(index);
                const displayText = isAccepted ? correction.suggested_correction : originalText;

                const backgroundColor = isAccepted ? 'rgba(76, 175, 80, 0.3)' : criteriaColors[criteriaType];
                const borderColor = isAccepted ? '#4caf50' : borderColors[criteriaType];

                const replacement = `<span class="criteria-highlight"
                    data-correction-index="${index}"
                    onclick="showCorrectionTooltip(event, ${index})"
                    style="background: ${backgroundColor}; border-bottom: 2px solid ${borderColor}; padding: 2px 6px; border-radius: 6px; cursor: pointer; transition: all 0.3s ease;"
                    onmouseover="this.style.transform='scale(1.02)'"
                    onmouseout="this.style.transform='scale(1)'"
                >${displayText}</span>`;

                const position = processedEssay.indexOf(originalText);
                if (position !== -1) {
                    processedEssay = processedEssay.substring(0, position) +
                                   replacement +
                                   processedEssay.substring(position + originalText.length);
                }
            });

            return processedEssay.replace(/\n/g, '<br><br>');
        }

        function generateCriteriaCorrectionsHTML(corrections, criteriaType) {
            const criteriaNames = {
                'task_achievement': 'Task Achievement',
                'coherence_cohesion': 'Coherence & Cohesion',
                'lexical_resource': 'Lexical Resource',
                'grammar_accuracy': 'Grammar Range & Accuracy'
            };

            if (corrections.length === 0) {
                return `
                    <div class="no-corrections">
                        <i class="fas fa-check-circle text-success"></i>
                        <h4>Tuyệt vời!</h4>
                        <p>Không có lỗi nào được tìm thấy cho tiêu chí <strong>${criteriaNames[criteriaType]}</strong></p>
                    </div>
                `;
            }

            return `
                <div class="criteria-corrections-header">
                    <h4><i class="fas fa-list"></i> Lỗi ${criteriaNames[criteriaType]} (${corrections.length})</h4>
                    <button class="btn btn-outline-secondary btn-sm" onclick="showAllCorrections()">
                        <i class="fas fa-eye"></i> Xem tất cả lỗi
                    </button>
                </div>
                ${corrections.map((correction, index) => {
                    return `
                    <div class="correction-card" onclick="showCorrectionTooltip(event, ${index})">
                        <div class="correction-header">
                            <div class="correction-number">${index + 1}</div>
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <span class="severity-badge severity-${correction.severity || 'medium'}">
                                    <i class="fas fa-${getSeverityIcon(correction.severity || 'medium')}"></i>
                                    ${correction.severity || 'medium'}
                                </span>
                                <span class="error-type-badge">
                                    ${correction.error_type || 'error'}
                                </span>
                            </div>
                        </div>
                        <div class="text-comparison">
                            <div class="original-text">
                                <strong><i class="fas fa-times"></i> Lỗi:</strong><br>
                                "${correction.original_text}"
                            </div>
                            <div class="suggested-text">
                                <strong><i class="fas fa-check"></i> Sửa thành:</strong><br>
                                "${correction.suggested_correction}"
                            </div>
                        </div>
                        <div class="correction-explanation">
                            <i class="fas fa-lightbulb"></i> ${correction.explanation || 'Cần sửa lỗi này để cải thiện bài viết.'}
                        </div>
                        ${correction.grammar_rule ? `
                            <div class="grammar-rule">
                                <i class="fas fa-book"></i> <strong>Quy tắc:</strong> ${correction.grammar_rule}
                            </div>
                        ` : ''}
                        ${correction.vocabulary_level ? `
                            <div class="vocabulary-level">
                                <i class="fas fa-graduation-cap"></i> <strong>Mức độ:</strong> ${correction.vocabulary_level}
                            </div>
                        ` : ''}
                        ${correction.spelling_rule ? `
                            <div class="spelling-rule">
                                <i class="fas fa-spell-check"></i> <strong>Chính tả:</strong> ${correction.spelling_rule}
                            </div>
                        ` : ''}
                        <div class="mt-2 text-center">
                            <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); highlightErrorInEssay('${correction.original_text}')">
                                <i class="fas fa-search"></i> Tìm trong bài
                            </button>
                        </div>
                    </div>
                `;
                }).join('')}
            `;
        }

        function showAllCorrections() {
            const data = currentEssayData || window.demoData;
            const essay = currentEssayText || window.sampleEssay;
            const corrections = data.highlighted_corrections || [];

            // Show all corrections
            const essayContent = document.getElementById('essayContent');
            const correctionsContainer = document.getElementById('correctionsContainer');

            if (essayContent) {
                essayContent.innerHTML = formatEssayWithHighlights(essay, corrections);
            }

            if (correctionsContainer) {
                correctionsContainer.innerHTML = generateCorrectionsHTML(corrections);
            }

            showToast('Thành công', `Đã hiển thị tất cả ${corrections.length} lỗi`, 'info');
        }

        function highlightErrorInEssay(originalText) {
            const essayContent = document.getElementById('essayContent');
            if (!essayContent) return;

            // Find and highlight the specific error
            const content = essayContent.innerHTML;
            const highlightedContent = content.replace(
                new RegExp(escapeRegExp(originalText), 'gi'),
                `<mark style="background: yellow; padding: 2px 4px; border-radius: 3px; animation: pulse 2s infinite;">${originalText}</mark>`
            );

            essayContent.innerHTML = highlightedContent;

            // Scroll to the highlighted text
            const highlightedElement = essayContent.querySelector('mark');
            if (highlightedElement) {
                highlightedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            showToast('Thành công', 'Đã highlight lỗi trong bài viết', 'success');
        }

        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }
    </script>
</body>
</html>
