<?php
require_once 'config.php';
require_once 'IELTSScorer.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Nuclear API Test</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: #f8f9fa; padding: 20px; }
        .container { max-width: 1200px; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .nuclear-success { background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .nuclear-fail { background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .correction-card { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .clean-badge { background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8em; }
        .dirty-badge { background: #dc3545; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8em; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1><i class='fas fa-radiation'></i> Nuclear API Cleaning Test</h1>";
echo "<p class='lead'>Testing the nuclear cleaning system with real API data</p>";

$testEssay = "Many people believes that education is important than ever. Students should only learn knowledge from books to do well in exam.";
$taskType = 'task2';
$prompt = 'Education is important. Discuss the role of books in learning.';

try {
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-edit'></i> Test Essay:</h5>";
    echo "<p>" . htmlspecialchars($testEssay) . "</p>";
    echo "</div>";
    
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-rocket'></i> Launching nuclear cleaning protocol...";
    echo "</div>";
    
    $scorer = new IELTSScorer();
    $result = $scorer->scoreEssay($testEssay, $taskType, $prompt);
    
    if (isset($result['error']) && $result['error']) {
        echo "<div class='nuclear-fail'>";
        echo "<h4><i class='fas fa-bomb'></i> Nuclear Cleaning Failed</h4>";
        echo "<p>Error: " . htmlspecialchars($result['message']) . "</p>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check'></i> API call successful! Overall score: " . ($result['overall_band_score'] ?? 'N/A');
        echo "</div>";
        
        if (isset($result['highlighted_corrections']) && is_array($result['highlighted_corrections'])) {
            $totalCorrections = count($result['highlighted_corrections']);
            $cleanCorrections = 0;
            $dirtyCorrections = 0;
            
            echo "<h3><i class='fas fa-microscope'></i> Nuclear Cleaning Analysis</h3>";
            echo "<p>Found {$totalCorrections} corrections. Analyzing cleanliness...</p>";
            
            foreach ($result['highlighted_corrections'] as $i => $correction) {
                $originalText = $correction['original_text'] ?? '';
                $suggestedText = $correction['suggested_correction'] ?? '';
                $explanation = $correction['explanation'] ?? '';
                
                // Check for any HTML artifacts
                $isClean = (strpos($originalText, 'data-') === false && 
                           strpos($originalText, '<') === false &&
                           strpos($originalText, '=') === false &&
                           strpos($suggestedText, 'data-') === false && 
                           strpos($suggestedText, '<') === false &&
                           strpos($explanation, 'data-') === false && 
                           strpos($explanation, '<') === false &&
                           !empty($originalText) && 
                           strlen($originalText) > 2);
                
                if ($isClean) {
                    $cleanCorrections++;
                    echo "<div class='correction-card'>";
                    echo "<div class='d-flex justify-content-between align-items-center'>";
                    echo "<h5><i class='fas fa-check-circle text-success'></i> Correction " . ($i + 1) . "</h5>";
                    echo "<span class='clean-badge'>NUCLEAR CLEAN</span>";
                    echo "</div>";
                    echo "<strong>Original:</strong> \"" . htmlspecialchars($originalText) . "\"<br>";
                    echo "<strong>Suggested:</strong> \"" . htmlspecialchars($suggestedText) . "\"<br>";
                    echo "<strong>Type:</strong> " . htmlspecialchars($correction['error_type'] ?? 'N/A') . "<br>";
                    echo "<strong>Severity:</strong> " . htmlspecialchars($correction['severity'] ?? 'N/A') . "<br>";
                    echo "<strong>Explanation:</strong> " . htmlspecialchars($explanation) . "<br>";
                    echo "<div class='text-success mt-2'><i class='fas fa-thumbs-up'></i> Ready for highlighting</div>";
                    echo "</div>";
                } else {
                    $dirtyCorrections++;
                    echo "<div class='correction-card' style='border-left-color: #dc3545;'>";
                    echo "<div class='d-flex justify-content-between align-items-center'>";
                    echo "<h5><i class='fas fa-exclamation-triangle text-danger'></i> Correction " . ($i + 1) . "</h5>";
                    echo "<span class='dirty-badge'>STILL DIRTY</span>";
                    echo "</div>";
                    echo "<strong>Original:</strong> \"" . htmlspecialchars($originalText) . "\"<br>";
                    echo "<strong>Suggested:</strong> \"" . htmlspecialchars($suggestedText) . "\"<br>";
                    echo "<div class='text-danger mt-2'><i class='fas fa-radiation'></i> Nuclear cleaning failed on this item</div>";
                    echo "</div>";
                }
            }
            
            // Nuclear cleaning results
            if ($cleanCorrections === $totalCorrections && $cleanCorrections > 0) {
                echo "<div class='nuclear-success'>";
                echo "<h3><i class='fas fa-trophy'></i> NUCLEAR SUCCESS!</h3>";
                echo "<p><strong>ALL {$totalCorrections} corrections are completely clean!</strong></p>";
                echo "<p><i class='fas fa-radiation'></i> Nuclear cleaning protocol successful. Highlighting system ready for deployment.</p>";
                echo "</div>";
            } else if ($cleanCorrections > 0) {
                echo "<div class='alert alert-warning'>";
                echo "<h4><i class='fas fa-exclamation-triangle'></i> Partial Nuclear Success</h4>";
                echo "<p><strong>{$cleanCorrections}</strong> out of {$totalCorrections} corrections are clean.</p>";
                echo "<p>{$dirtyCorrections} corrections still contain artifacts.</p>";
                echo "</div>";
            } else {
                echo "<div class='nuclear-fail'>";
                echo "<h3><i class='fas fa-skull-crossbones'></i> NUCLEAR FAILURE!</h3>";
                echo "<p>No clean corrections found. All {$totalCorrections} corrections contain HTML artifacts.</p>";
                echo "<p>The AI is still returning HTML despite nuclear cleaning protocols.</p>";
                echo "</div>";
            }
            
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times'></i> No highlighted_corrections found in response";
            echo "</div>";
        }
    }
    
    echo "<details class='mt-4'>";
    echo "<summary><i class='fas fa-code'></i> Raw Nuclear Response (for debugging)</summary>";
    echo "<pre class='bg-light p-3 rounded'>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>";
    echo "</details>";
    
} catch (Exception $e) {
    echo "<div class='nuclear-fail'>";
    echo "<h4><i class='fas fa-bomb'></i> Nuclear Exception</h4>";
    echo "<p>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</body></html>";
?>
