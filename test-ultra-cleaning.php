<?php
// Test ultra cleaning functions

function ultraCleanText($text) {
    if (!$text) return '';
    
    // First pass: standard cleaning
    $cleaned = cleanTextContent($text);
    
    // If still contains artifacts, do manual extraction
    if (strpos($cleaned, 'data-') !== false || strpos($cleaned, '="') !== false) {
        $cleaned = extractCleanText($text);
    }
    
    return $cleaned;
}

function cleanTextContent($text) {
    if (!$text) return '';
    
    // Step 1: Remove all HTML tags completely
    $text = strip_tags($text);
    
    // Step 2: Decode HTML entities
    $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    // Step 3: Remove ALL data attributes (aggressive pattern)
    $text = preg_replace('/data-[a-zA-Z0-9-]+=["\'"][^"\']*["\'"]/', '', $text);
    $text = preg_replace('/data-[a-zA-Z0-9-]+=["\'"][^"\']*["\'"]/', '', $text); // Run twice for nested
    
    // Step 4: Remove title, class, id attributes
    $text = preg_replace('/title=["\'"][^"\']*["\'"]/', '', $text);
    $text = preg_replace('/class=["\'"][^"\']*["\'"]/', '', $text);
    $text = preg_replace('/id=["\'"][^"\']*["\'"]/', '', $text);
    
    // Step 5: Remove ANY remaining HTML-like attributes
    $text = preg_replace('/[a-zA-Z0-9-]+=["\'"][^"\']*["\'"]/', '', $text);
    
    // Step 6: Remove orphaned symbols from HTML cleanup
    $text = preg_replace('/^\s*[>]+\s*/', '', $text);
    $text = preg_replace('/\s*[>]+\s*$/', '', $text);
    $text = preg_replace('/\s*[>]+\s*/', ' ', $text);
    
    // Step 7: Remove any remaining HTML-like patterns
    $text = preg_replace('/<[^>]*>/', '', $text);
    
    // Step 8: Extract only the actual text content
    // Look for patterns like: 'attribute="value">actual text'
    if (preg_match('/["\']>\s*([^<]+)/', $text, $matches)) {
        $text = $matches[1];
    }
    
    // Step 9: Clean up extra whitespace
    $text = preg_replace('/\s+/', ' ', $text);
    $text = trim($text);
    
    // Step 10: If still contains HTML artifacts, extract text manually
    if (strpos($text, 'data-') !== false || strpos($text, '="') !== false) {
        // Last resort: extract text after the last '>' or '"'
        $parts = preg_split('/[>"]/', $text);
        $text = end($parts);
        $text = trim($text);
    }
    
    return $text;
}

function extractCleanText($text) {
    if (!$text) return '';
    
    // Method 1: Look for text after the last '>' character
    if (preg_match('/>[^<]*$/', $text, $matches)) {
        $extracted = trim(str_replace('>', '', $matches[0]));
        if (!empty($extracted) && strpos($extracted, 'data-') === false) {
            return $extracted;
        }
    }
    
    // Method 2: Look for text between quotes that looks like actual content
    if (preg_match_all('/"([^"]*)"/', $text, $matches)) {
        foreach ($matches[1] as $match) {
            if (strlen($match) > 3 && strpos($match, 'data-') === false && strpos($match, 'correction') === false) {
                return $match;
            }
        }
    }
    
    // Method 3: Split by common delimiters and find the longest clean part
    $parts = preg_split('/[<>"]/', $text);
    $cleanest = '';
    foreach ($parts as $part) {
        $part = trim($part);
        if (strlen($part) > strlen($cleanest) && 
            strpos($part, 'data-') === false && 
            strpos($part, '=') === false &&
            !empty($part)) {
            $cleanest = $part;
        }
    }
    
    return $cleanest;
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>Ultra Cleaning Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .dirty { background: #ffe6e6; }
        .clean { background: #e6ffe6; }
        .result { font-weight: bold; margin-top: 10px; }
        .success { color: green; }
        .fail { color: red; }
    </style>
</head>
<body>";

echo "<h1>Ultra Aggressive Text Cleaning Test</h1>";

// Real problematic cases from the screenshot
$realCases = [
    'data-correction-id="correction-6" data-original="education is important than ever" data-suggestion="education is more important than ever" data-explanation="Comparative form is incorrect; \'more\' is needed." data-type="grammar" data-severity="high" title="Click for suggestion">education is important than ever',
    
    'data-correction-id="correction-2" data-original="students should only learn knowledge from books" data-suggestion="students should primarily learn knowledge from books" data-explanation="The word \'only\' implies exclusivity, which is not the intended meaning." data-type="vocabulary" data-severity="medium" title="Click for suggestion">students should only learn knowledge from books',
    
    'data-correction-id="correction-1">I don\'t believe that students should only learn knowledge from books',
    
    'title="Click for suggestion">people believes',
    
    'data-type="grammar" data-severity="high">technology have changed'
];

foreach ($realCases as $i => $testCase) {
    $cleaned = ultraCleanText($testCase);
    
    echo "<div class='test'>";
    echo "<h3>Real Case " . ($i + 1) . "</h3>";
    echo "<div class='dirty'><strong>Original:</strong><br>" . htmlspecialchars($testCase) . "</div>";
    echo "<div class='clean'><strong>Ultra Cleaned:</strong><br>" . htmlspecialchars($cleaned) . "</div>";
    
    // Check if cleaning was successful
    $hasArtifacts = (strpos($cleaned, 'data-') !== false || 
                    strpos($cleaned, '="') !== false || 
                    strpos($cleaned, '<') !== false ||
                    strpos($cleaned, '>') !== false ||
                    strpos($cleaned, 'correction') !== false);
    
    if ($hasArtifacts) {
        echo "<div class='result fail'>❌ Still contains artifacts</div>";
    } else if (empty($cleaned)) {
        echo "<div class='result fail'>❌ No text extracted</div>";
    } else {
        echo "<div class='result success'>✅ Successfully cleaned: \"" . htmlspecialchars($cleaned) . "\"</div>";
    }
    
    echo "</div>";
}

echo "</body></html>";
?>
