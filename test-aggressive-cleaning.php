<?php
// Test aggressive cleaning function

function cleanTextContent($text) {
    if (!$text) return '';
    
    // Step 1: Remove all HTML tags completely
    $text = strip_tags($text);
    
    // Step 2: Decode HTML entities
    $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    // Step 3: Remove ALL data attributes (aggressive pattern)
    $text = preg_replace('/data-[a-zA-Z0-9-]+=["\'"][^"\']*["\'"]/', '', $text);
    $text = preg_replace('/data-[a-zA-Z0-9-]+=["\'"][^"\']*["\'"]/', '', $text); // Run twice for nested
    
    // Step 4: Remove title, class, id attributes
    $text = preg_replace('/title=["\'"][^"\']*["\'"]/', '', $text);
    $text = preg_replace('/class=["\'"][^"\']*["\'"]/', '', $text);
    $text = preg_replace('/id=["\'"][^"\']*["\'"]/', '', $text);
    
    // Step 5: Remove ANY remaining HTML-like attributes
    $text = preg_replace('/[a-zA-Z0-9-]+=["\'"][^"\']*["\'"]/', '', $text);
    
    // Step 6: Remove orphaned symbols from HTML cleanup
    $text = preg_replace('/^\s*[>]+\s*/', '', $text);
    $text = preg_replace('/\s*[>]+\s*$/', '', $text);
    $text = preg_replace('/\s*[>]+\s*/', ' ', $text);
    
    // Step 7: Remove any remaining HTML-like patterns
    $text = preg_replace('/<[^>]*>/', '', $text);
    
    // Step 8: Extract only the actual text content
    // Look for patterns like: 'attribute="value">actual text'
    if (preg_match('/["\']>\s*([^<]+)/', $text, $matches)) {
        $text = $matches[1];
    }
    
    // Step 9: Clean up extra whitespace
    $text = preg_replace('/\s+/', ' ', $text);
    $text = trim($text);
    
    // Step 10: If still contains HTML artifacts, extract text manually
    if (strpos($text, 'data-') !== false || strpos($text, '="') !== false) {
        // Last resort: extract text after the last '>' or '"'
        $parts = preg_split('/[>"]/', $text);
        $text = end($parts);
        $text = trim($text);
    }
    
    return $text;
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>Aggressive Cleaning Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .dirty { background: #ffe6e6; }
        .clean { background: #e6ffe6; }
        .result { font-weight: bold; color: #007bff; }
    </style>
</head>
<body>";

echo "<h1>Aggressive Text Cleaning Test</h1>";

// Test cases based on the actual problematic data from the screenshot
$testCases = [
    'data-correction-id="correction-6" data-original="education is important than ever" data-suggestion="education is more important than ever" data-explanation="Comparative form is incorrect; \'more\' is needed." data-type="grammar" data-severity="high" title="Click for suggestion">education is important than ever',
    
    'data-correction-id="correction-2" data-original="students should only learn knowledge from books" data-suggestion="students should primarily learn knowledge from books" data-explanation="The word \'only\' implies exclusivity, which is not the intended meaning." data-type="vocabulary" data-severity="medium" title="Click for suggestion">students should only learn knowledge from books',
    
    '<span class="highlight grammar high">people believes</span>',
    
    'Normal text without any HTML',
    
    'data-type="grammar">education is important',
    
    'title="Click for suggestion">some text here',
    
    'Multiple data-attr="value1" data-other="value2" title="test">final text content'
];

foreach ($testCases as $i => $testCase) {
    $cleaned = cleanTextContent($testCase);
    
    echo "<div class='test'>";
    echo "<h3>Test Case " . ($i + 1) . "</h3>";
    echo "<div class='dirty'><strong>Original:</strong><br>" . htmlspecialchars($testCase) . "</div>";
    echo "<div class='clean'><strong>Cleaned:</strong><br>" . htmlspecialchars($cleaned) . "</div>";
    
    // Check if cleaning was successful
    $hasArtifacts = (strpos($cleaned, 'data-') !== false || 
                    strpos($cleaned, '="') !== false || 
                    strpos($cleaned, '<') !== false ||
                    strpos($cleaned, '>') !== false);
    
    if ($hasArtifacts) {
        echo "<div style='color: red;'>❌ Still contains HTML artifacts</div>";
    } else {
        echo "<div style='color: green;'>✅ Successfully cleaned</div>";
    }
    
    echo "<div class='result'>Length: " . strlen($cleaned) . " characters</div>";
    echo "</div>";
}

echo "</body></html>";
?>
