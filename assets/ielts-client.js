/**
 * IELTS Scoring Client - Handles API communication and UI updates
 */
class IELTSClient {
    constructor() {
        this.apiUrl = 'api.php';
        this.isProcessing = false;
        this.highlighter = null;
        this.progressTracker = null;
        
        this.initializeComponents();
        this.bindEvents();
    }
    
    initializeComponents() {
        // Initialize highlighter and progress tracker
        this.highlighter = new EssayHighlighter('highlightedEssayContainer');
        this.progressTracker = new IELTSProgressTracker();
        
        console.log('IELTS Client initialized');
    }
    
    bindEvents() {
        // Bind form submission
        const form = document.getElementById('essayForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSubmit();
            });
        }
        
        // Bind word count
        const essayTextarea = document.getElementById('essay');
        if (essayTextarea) {
            essayTextarea.addEventListener('input', this.updateWordCount);
        }
    }
    
    updateWordCount() {
        const essayTextarea = document.getElementById('essay');
        const wordCountDiv = document.getElementById('wordCount');
        
        if (essayTextarea && wordCountDiv) {
            const text = essayTextarea.value.trim();
            const wordCount = text === '' ? 0 : text.split(/\s+/).length;
            wordCountDiv.textContent = `Word count: ${wordCount}`;
            
            // Update color based on word count
            if (wordCount < 150) {
                wordCountDiv.className = 'text-danger small';
            } else if (wordCount > 300) {
                wordCountDiv.className = 'text-warning small';
            } else {
                wordCountDiv.className = 'text-success small';
            }
        }
    }
    
    async handleSubmit() {
        if (this.isProcessing) {
            console.log('Already processing, ignoring submit');
            return;
        }
        
        try {
            // Get form data
            const formData = this.getFormData();
            
            // Validate form data
            this.validateFormData(formData);
            
            // Show loading state
            this.showLoadingState();
            
            // Call API
            const result = await this.callAPI(formData);
            
            // Process and display results
            this.displayResults(result, formData.essay);
            
        } catch (error) {
            console.error('Submit error:', error);
            this.displayError(error.message);
        } finally {
            this.hideLoadingState();
        }
    }
    
    getFormData() {
        return {
            essay: document.getElementById('essay')?.value || '',
            task_type: document.getElementById('task_type')?.value || '',
            prompt: document.getElementById('prompt')?.value || ''
        };
    }
    
    validateFormData(data) {
        if (!data.essay.trim()) {
            throw new Error('Please enter your essay text');
        }
        
        if (!data.task_type) {
            throw new Error('Please select a task type');
        }
        
        const wordCount = data.essay.trim().split(/\s+/).length;
        if (wordCount < 10) {
            throw new Error('Essay is too short. Please write at least 10 words.');
        }
    }
    
    async callAPI(data) {
        console.log('Calling API with data:', data);
        
        const response = await fetch(this.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        console.log('API Response:', result);
        
        if (!response.ok) {
            throw new Error(result.message || `HTTP ${response.status}`);
        }
        
        if (result.error) {
            throw new Error(result.message || 'API returned an error');
        }
        
        return result;
    }
    
    showLoadingState() {
        this.isProcessing = true;
        
        // Hide previous results
        this.hideAllSections();
        
        // Show loading
        const loadingDiv = document.getElementById('loadingSection');
        if (loadingDiv) {
            loadingDiv.style.display = 'block';
        }
        
        // Disable submit button
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        }
        
        console.log('Loading state shown');
    }
    
    hideLoadingState() {
        this.isProcessing = false;
        
        // Hide loading
        const loadingDiv = document.getElementById('loadingSection');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
        
        // Enable submit button
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-check"></i> Score My Essay';
        }
        
        console.log('Loading state hidden');
    }
    
    hideAllSections() {
        const sections = ['loadingSection', 'resultsSection', 'errorSection'];
        sections.forEach(sectionId => {
            const section = document.getElementById(sectionId);
            if (section) {
                section.style.display = 'none';
            }
        });
    }
    
    displayResults(result, originalEssay) {
        console.log('Displaying results:', result);
        
        try {
            // Show results section
            const resultsSection = document.getElementById('resultsSection');
            if (resultsSection) {
                resultsSection.style.display = 'block';
            }
            
            // Update overall score
            this.updateOverallScore(result);
            
            // Update detailed scores
            this.updateDetailedScores(result);
            
            // Update corrections and highlighting
            this.updateCorrections(result, originalEssay);
            
            // Update metadata
            this.updateMetadata(result);
            
            // Track progress
            if (this.progressTracker) {
                this.progressTracker.addSession(result, originalEssay, result.request_metadata?.task_type);
            }
            
            console.log('Results displayed successfully');
            
        } catch (error) {
            console.error('Error displaying results:', error);
            this.displayError('Error displaying results: ' + error.message);
        }
    }
    
    updateOverallScore(result) {
        const scoreElement = document.getElementById('overallScore');
        const descElement = document.getElementById('scoreDescription');
        
        if (scoreElement) {
            scoreElement.textContent = result.overall_band_score || 'N/A';
        }
        
        if (descElement && result.overall_band_score) {
            // You can add band description logic here
            descElement.textContent = this.getBandDescription(result.overall_band_score);
        }
    }
    
    updateDetailedScores(result) {
        // Update individual criteria scores if available
        const criteria = ['task_achievement', 'coherence_cohesion', 'lexical_resource', 'grammatical_range'];
        
        criteria.forEach(criterion => {
            const element = document.getElementById(criterion + '_score');
            if (element && result[criterion]) {
                element.textContent = result[criterion];
            }
        });
    }
    
    updateCorrections(result, originalEssay) {
        if (!result.highlighted_corrections || !Array.isArray(result.highlighted_corrections)) {
            console.log('No corrections to display');
            this.showNoCorrections(originalEssay);
            return;
        }

        console.log('Raw corrections received:', result.highlighted_corrections);

        // ULTRA VALIDATION - Filter out any dirty corrections
        const cleanCorrections = this.validateAndCleanCorrections(result.highlighted_corrections);

        console.log(`Validation result: ${cleanCorrections.length} clean out of ${result.highlighted_corrections.length} total`);

        if (cleanCorrections.length === 0) {
            console.warn('No clean corrections found after validation');
            this.showNoCorrections(originalEssay, 'All corrections contain HTML artifacts and were filtered out for safety.');
            return;
        }

        try {
            // Update result with clean corrections only
            const cleanResult = {
                ...result,
                highlighted_corrections: cleanCorrections
            };

            // Initialize highlighting with clean data
            if (this.highlighter) {
                this.highlighter.init(cleanResult, originalEssay);
                console.log('Highlighting initialized successfully with clean data');
            }

            // Update corrections list
            this.updateCorrectionsList(cleanCorrections);

            // Show cleaning stats
            this.showCleaningStats(result.highlighted_corrections.length, cleanCorrections.length);

        } catch (error) {
            console.error('Error updating corrections:', error);
            this.showNoCorrections(originalEssay, 'Error processing corrections: ' + error.message);
        }
    }

    validateAndCleanCorrections(corrections) {
        const cleanCorrections = [];

        for (const correction of corrections) {
            const originalText = correction.original_text || '';
            const suggestedText = correction.suggested_correction || '';
            const explanation = correction.explanation || '';

            // Ultra strict validation
            const isClean = (
                originalText.length > 2 &&
                suggestedText.length > 0 &&
                originalText.indexOf('data-') === -1 &&
                originalText.indexOf('<') === -1 &&
                originalText.indexOf('>') === -1 &&
                originalText.indexOf('=') === -1 &&
                originalText.indexOf('correction-') === -1 &&
                originalText.indexOf('title=') === -1 &&
                suggestedText.indexOf('data-') === -1 &&
                suggestedText.indexOf('<') === -1 &&
                explanation.indexOf('data-') === -1 &&
                explanation.indexOf('<') === -1
            );

            if (isClean) {
                cleanCorrections.push(correction);
                console.log('✅ Clean correction:', originalText);
            } else {
                console.warn('❌ Dirty correction filtered out:', originalText);
            }
        }

        return cleanCorrections;
    }

    showNoCorrections(originalEssay, reason = 'No corrections found.') {
        const container = document.getElementById('highlightedEssayContainer');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    ${reason}
                </div>
                <div class="essay-content" style="padding: 20px; background: #f8f9fa; border-radius: 10px; margin-top: 15px;">
                    ${originalEssay.replace(/\n/g, '<br>')}
                </div>
            `;
        }

        // Clear corrections list
        const correctionsList = document.getElementById('correctionsList');
        if (correctionsList) {
            correctionsList.innerHTML = `<p class="text-muted">${reason}</p>`;
        }
    }

    showCleaningStats(originalCount, cleanCount) {
        if (originalCount > cleanCount) {
            const container = document.getElementById('highlightedEssayContainer');
            if (container) {
                const statsDiv = document.createElement('div');
                statsDiv.className = 'alert alert-warning mt-2';
                statsDiv.innerHTML = `
                    <i class="fas fa-filter"></i>
                    <strong>Cleaning Applied:</strong> ${cleanCount} out of ${originalCount} corrections were clean and safe to display.
                    ${originalCount - cleanCount} corrections contained HTML artifacts and were filtered out.
                `;
                container.appendChild(statsDiv);
            }
        }
    }
    
    updateCorrectionsList(corrections) {
        const container = document.getElementById('correctionsList');
        if (!container) return;
        
        if (corrections.length === 0) {
            container.innerHTML = '<p class="text-muted">No corrections found.</p>';
            return;
        }
        
        const html = corrections.map((correction, index) => `
            <div class="correction-item mb-3 p-3 border rounded">
                <h6>Correction ${index + 1}</h6>
                <p><strong>Original:</strong> "${correction.original_text || 'N/A'}"</p>
                <p><strong>Suggested:</strong> "${correction.suggested_correction || 'N/A'}"</p>
                <p><strong>Type:</strong> ${correction.error_type || 'N/A'}</p>
                <p><strong>Explanation:</strong> ${correction.explanation || 'N/A'}</p>
                <span class="badge bg-${this.getSeverityColor(correction.severity)}">${correction.severity || 'medium'}</span>
            </div>
        `).join('');
        
        container.innerHTML = html;
    }
    
    updateMetadata(result) {
        const metadataElement = document.getElementById('metadata');
        if (metadataElement && result.request_metadata) {
            const metadata = result.request_metadata;
            metadataElement.innerHTML = `
                <small class="text-muted">
                    Scored at: ${metadata.timestamp} | 
                    Word count: ${metadata.word_count} | 
                    Processing time: ${(metadata.processing_time || 0).toFixed(2)}s
                </small>
            `;
        }
    }
    
    displayError(message) {
        console.error('Displaying error:', message);
        
        // Hide other sections
        this.hideAllSections();
        
        // Show error section
        const errorSection = document.getElementById('errorSection');
        if (errorSection) {
            errorSection.style.display = 'block';
            errorSection.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Error:</strong> ${message}
                    <div class="mt-2">
                        <small class="text-muted">
                            Please check your internet connection and try again. 
                            If the problem persists, try with a shorter essay.
                        </small>
                    </div>
                </div>
            `;
        }
    }
    
    getBandDescription(score) {
        const descriptions = {
            9: 'Expert User',
            8: 'Very Good User',
            7: 'Good User',
            6: 'Competent User',
            5: 'Modest User',
            4: 'Limited User',
            3: 'Extremely Limited User',
            2: 'Intermittent User',
            1: 'Non User'
        };
        
        return descriptions[Math.floor(score)] || 'User';
    }
    
    getSeverityColor(severity) {
        const colors = {
            'high': 'danger',
            'medium': 'warning',
            'low': 'info'
        };
        return colors[severity] || 'secondary';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing IELTS Client...');
    window.ieltsClient = new IELTSClient();
});
