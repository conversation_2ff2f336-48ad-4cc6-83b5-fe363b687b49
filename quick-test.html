<!DOCTYPE html>
<html>
<head>
    <title>Quick Highlighting Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/essay-highlighter.css" rel="stylesheet">
</head>
<body class="p-4">
    <div class="container">
        <h2>Quick Highlighting Test</h2>
        <div id="testContainer"></div>
        <button class="btn btn-primary mt-3" onclick="runTest()">Run Test</button>
    </div>

    <script src="assets/essay-highlighter.js"></script>
    <script>
        function runTest() {
            const essay = "Many people believes that technology have changed our lifes dramatically.";
            const result = {
                highlighted_corrections: [
                    {
                        original_text: "people believes",
                        suggested_correction: "people believe",
                        error_type: "grammar",
                        explanation: "Subject-verb agreement",
                        severity: "high"
                    },
                    {
                        original_text: "lifes",
                        suggested_correction: "lives",
                        error_type: "vocabulary",
                        explanation: "Spelling error",
                        severity: "medium"
                    }
                ]
            };

            const highlighter = new EssayHighlighter('testContainer');
            highlighter.init(result, essay);
        }
    </script>
</body>
</html>
