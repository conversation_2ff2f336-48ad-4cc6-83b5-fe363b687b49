<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php require_once 'config.php'; echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/essay-highlighter.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .header h1 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .form-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .result-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .score-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .score-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .criteria-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .criteria-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            border-left: 4px solid #007bff;
        }

        .essay-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 200px;
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
        }

        .correction-item {
            transition: all 0.3s ease;
        }

        .correction-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        #wordCount {
            font-weight: 500;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .debug-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-graduation-cap"></i> <?php echo APP_NAME; ?></h1>
                <p class="lead">Get instant, detailed feedback on your IELTS Writing tasks with AI-powered scoring</p>
            </div>

            <!-- Essay Form -->
            <div class="form-section">
                <h2><i class="fas fa-edit"></i> Submit Your Essay</h2>
                <form id="essayForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="task_type" class="form-label">Task Type</label>
                            <select class="form-select" id="task_type" required>
                                <option value="">Select task type...</option>
                                <option value="task1_academic">Task 1 Academic (Graph/Chart/Diagram)</option>
                                <option value="task1_general">Task 1 General Training (Letter)</option>
                                <option value="task2">Task 2 (Essay)</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="prompt" class="form-label">Task Prompt (Optional)</label>
                            <input type="text" class="form-control" id="prompt" 
                                   placeholder="Enter the original task prompt...">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="essay" class="form-label">Your Essay</label>
                        <textarea class="form-control" id="essay" rows="12" required
                                  placeholder="Paste your IELTS writing response here..."></textarea>
                        <div id="wordCount" class="text-muted small mt-1">Word count: 0</div>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-check"></i> Score My Essay
                        </button>
                    </div>
                </form>
            </div>

            <!-- Loading Section -->
            <div id="loadingSection" style="display: none;">
                <div class="result-section">
                    <div class="loading-spinner">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h4 class="mt-3">Analyzing Your Essay...</h4>
                            <p class="text-muted">This may take a few moments. Please wait...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Section -->
            <div id="errorSection" style="display: none;">
                <!-- Error content will be populated by JavaScript -->
            </div>

            <!-- Results Section -->
            <div id="resultsSection" style="display: none;">
                <div class="result-section">
                    <h2><i class="fas fa-chart-bar"></i> Scoring Results</h2>

                    <!-- Overall Score -->
                    <div class="score-card">
                        <div class="score-number" id="overallScore">0</div>
                        <h4>Overall Band Score</h4>
                        <p id="scoreDescription">Your IELTS Writing Band Score</p>
                    </div>

                    <!-- Detailed Scores -->
                    <div class="criteria-grid">
                        <div class="criteria-card">
                            <h6><i class="fas fa-target"></i> Task Achievement</h6>
                            <div class="h4 text-primary" id="task_achievement_score">-</div>
                        </div>
                        <div class="criteria-card">
                            <h6><i class="fas fa-link"></i> Coherence & Cohesion</h6>
                            <div class="h4 text-primary" id="coherence_cohesion_score">-</div>
                        </div>
                        <div class="criteria-card">
                            <h6><i class="fas fa-book"></i> Lexical Resource</h6>
                            <div class="h4 text-primary" id="lexical_resource_score">-</div>
                        </div>
                        <div class="criteria-card">
                            <h6><i class="fas fa-language"></i> Grammar Range & Accuracy</h6>
                            <div class="h4 text-primary" id="grammatical_range_score">-</div>
                        </div>
                    </div>

                    <!-- Essay with Corrections -->
                    <div class="row">
                        <div class="col-lg-8">
                            <h4><i class="fas fa-highlighter"></i> Your Essay with Corrections</h4>
                            <div class="essay-container" id="highlightedEssayContainer">
                                <!-- Highlighted essay will be populated here -->
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <h4><i class="fas fa-list"></i> Corrections</h4>
                            <div id="correctionsList">
                                <!-- Corrections list will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Metadata -->
                    <div id="metadata" class="text-center mt-3">
                        <!-- Metadata will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Debug Panel (for development) -->
            <?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
            <div class="debug-panel">
                <h5><i class="fas fa-bug"></i> Debug Panel</h5>
                <div id="debugInfo">
                    <p>Debug information will appear here during development.</p>
                    <button class="btn btn-sm btn-outline-secondary" onclick="console.log('Debug:', window.ieltsClient)">
                        Log Client State
                    </button>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/essay-highlighter.js"></script>
    <script src="assets/progress-tracker.js"></script>
    <script src="assets/ielts-client.js"></script>

    <script>
        // Additional debugging for development
        <?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
        console.log('Debug mode enabled');
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
            const debugInfo = document.getElementById('debugInfo');
            if (debugInfo) {
                debugInfo.innerHTML += `<div class="alert alert-danger mt-2">
                    <strong>Error:</strong> ${e.message}<br>
                    <small>File: ${e.filename}:${e.lineno}</small>
                </div>`;
            }
        });
        <?php endif; ?>
    </script>
</body>
</html>
